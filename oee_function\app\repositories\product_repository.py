from ast import Dict
from typing import Iterator, Optional

import pandas as pd
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from cognite.client.data_classes.data_modeling.instances import NodeApply

from ..enums.entity_code import EntityCode
from ..models.node import Node
from ..models.product import Product
from ..models.reporting_site import ReportingSite
from ..utils.graphql import generate_query, query_all
from ..utils.id_generation import IdGenerator
from ..utils.list import divide_in_chunks
from ..utils.space import get_space
from .view_repository import ViewRepository


class ProductRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
        data_model_id: DataModelId,
        default_instances_space: str,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self._data_model_id = data_model_id
        self._default_instances_space = default_instances_space

    def get_oee_product_df(
        self,
        product_names: list[str],
        reporting_site: ReportingSite,
        create_missing: bool = False,
        id_generator: Optional[IdGenerator[EntityCode]] = IdGenerator[
            EntityCode
        ](),
    ) -> pd.DataFrame:
        name_to_product_map: Dict[str, dict[str, str]] = {}
        for product_names_chunk in divide_in_chunks(product_names, 1000):
            filter_ = {
                "and": [
                    {"name": {"in": product_names_chunk}},
                    {
                        "refSite": {
                            "externalId": {"eq": reporting_site.external_id}
                        }
                    },
                ]
            }

            list_name = "listOEEProduct"

            query_result = query_all(
                client=self._cognite_client,
                data_model_id=self._data_model_id,
                list_name=list_name,
                query=generate_query(
                    list_name, self._build_product_query_selection()
                ),
                filter=filter_,
            )

            if len(query_result) > 0:
                name_to_product_map.update(
                    {p["name"]: p for p in query_result}
                )

        if not create_missing:
            return self._convert_product_dicts_to_df(
                list(name_to_product_map.values())
            )

        site_node = reporting_site.convert_to_node_reference()
        country_node = (
            reporting_site.country.convert_to_node_reference()
            if reporting_site.country is not None
            else None
        )
        region_node = (
            reporting_site.country.parent.convert_to_node_reference()
            if country_node is not None
            and reporting_site.country.parent is not None
            else None
        )

        space = get_space(
            reporting_site_code=reporting_site.site_code,
            default=self._default_instances_space,
            is_adm=True,
        )

        view = self._get_view_id()

        products_to_create: list[NodeApply] = []
        for name in product_names:
            if name in name_to_product_map:
                continue

            ext_id = id_generator.next_id(EntityCode.OEE_PRODUCT)

            name_to_product_map[name] = {
                "externalId": ext_id,
                "space": space,
                "name": name,
            }

            product = Product(
                externalId=ext_id,
                space=space,
                name=str(name),
                description=str(name),
                refRegion=region_node,
                refCountry=country_node,
                refSite=site_node,
            )

            products_to_create.append(
                product.convert_to_cognite_node(view_id=view)
            )

        for products_to_create_chunk in divide_in_chunks(
            products_to_create, 1000
        ):
            self._cognite_client.data_modeling.instances.apply(
                nodes=products_to_create_chunk
            )

        return self._convert_product_dicts_to_df(
            list(name_to_product_map.values())
        )

    def _get_view_id(self):
        return self._view_repository.get_view_id("OEEProduct")

    @staticmethod
    def _convert_product_dicts_to_df(dicts: list[dict]) -> pd.DataFrame:
        if len(dicts) > 0:
            return pd.DataFrame.from_records(dicts)
        return pd.DataFrame(columns=["externalId", "space", "name"])

    @staticmethod
    def _build_product_query_selection():
        return """
externalId
space
name
    """
