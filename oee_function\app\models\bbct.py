from datetime import datetime
from typing import Any, Optional

from pydantic import Field, computed_field

from .node import Node
from .reporting_line import ReportingLine
from .reporting_site import ReportingSite
from .material import Material


class Bbct(Node):
    reporting_site: ReportingSite = Field(alias="refSite")
    reporting_line: ReportingLine = Field(alias="refReportingLine")
    product: str = Field(default=None, alias="product")
    pi_tag_value: str = Field(default=None, alias="pITagValue")
    date_set: str = Field(default=None, alias="dateSet")
    best_batch_cycle_time_day: float = Field(
        default=None, alias="bestBatchCycleTimeDay"
    )
    best_batch_cycle_time_hr: float = Field(
        default=None, alias="bestBatchCycleTimeHr"
    )
    best_batch_cycle_time_mt: Optional[float] = Field(
        default=None, alias="bestBatchCycleTimeMT"
    )
    lead_bbct: Optional[float] = Field(
        default=None, alias="leadBBCT"
    )
    active: Optional[bool] = Field(default=None, alias="active")
    refMaterial: Optional[Material] = Field(default=None, alias="refMaterial")

    @computed_field
    @property
    def date_set_datetime(self) -> datetime:
        return datetime.fromisoformat(self.date_set)

    @computed_field
    @property
    def month(self) -> int:
        return self.date_set_datetime.month

    @computed_field
    @property
    def year(self) -> int:
        return self.date_set_datetime.year

    @computed_field
    @property
    def timestamp(self) -> datetime:
        return datetime(self.year, self.month, 1)

    @computed_field
    @property
    def bbct(self) -> float:
        return self.best_batch_cycle_time_hr * 3600

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "Bbct":
        return Bbct(
            externalId=item["externalId"],
            space=item["space"],
            refSite=ReportingSite(**item["refSite"]),
            refReportingLine=ReportingLine(**item["refReportingLine"]),
            pITagValue=item["pITagValue"],
            dateSet=item["dateSet"],
            bestBatchCycleTimeHr=item["bestBatchCycleTimeHr"],
            bestBatchCycleTimeMT=item["bestBatchCycleTimeMT"],
            leadBBCT=item["leadBBCT"] * 3600 if item["leadBBCT"] is not None else None,
        )
