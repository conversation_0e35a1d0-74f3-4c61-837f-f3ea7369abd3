from datetime import datetime, timed<PERSON>ta
from typing import List, Optional

import numpy as np
import pandas as pd
from app.models.input_tag_configuration import InputTagConfiguration
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from .was.was_general_service import WashingtonGeneralService


class HourlyDataCompounding:
    def __init__(self, reporting_line_external_id: str) -> None:
        self._reporting_line_external_id = reporting_line_external_id

    def identify_events_typeIII(
        self,
        data: pd.DataFrame,
        prod_rate_data: pd.DataFrame,
        mdr: Optional[pd.DataFrame], # TODO (EP): tornar MDR opcional
        configurations: ReportingSiteConfiguration,
        data_timeseries_configurations: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        identifies events of type III according to the business rules

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        # fill information to avoid wrong calculation

        prod_rate_data.fillna(
            value={
                col: "-"
                for col in prod_rate_data.select_dtypes(
                    include=["object", "string"]
                ).columns
            },
            inplace=True,
        )

        prod_rate_data.fillna(
            value={
                col: 0
                for col in prod_rate_data.select_dtypes(
                    include=["number"]
                ).columns
            },
            inplace=True,
        )

        prod_hourly_data = prod_rate_data.copy()

        prod_hourly_data.reset_index(inplace=True)

        # calculate Running Time using ProductionLineStatus

        prod_hourly_data["running_duration"] = (
            prod_hourly_data["index"].diff(-1).dt.total_seconds().abs()
        )

        if configurations.reporting_site.external_id == "STS-PEN":
            condition_where = prod_hourly_data["ProductionLineStatus"] > 0
        elif configurations.reporting_site.external_id == "STS-FOR":
            condition_where = (
                (prod_hourly_data["ProductionLineStatus"] == 0)
                & (prod_hourly_data["BatchID"].astype(str).str.startswith("000"))
            )
        elif configurations.reporting_site.external_id == "STS-FRA":
            condition_where = (
                (prod_hourly_data["ProductionLineStatus"] == "On") &
                (prod_hourly_data["StartingUp"] == "On")
            )
        elif configurations.reporting_site.external_id == "STS-WAS":
            washington_general_service = WashingtonGeneralService(
                reporting_line_external_id=self._reporting_line_external_id
            )
            
            prod_hourly_data = washington_general_service.prepare_hourly_data(
                data=prod_hourly_data,
            )
            
            condition_where = (prod_hourly_data["isRunning"] == True)
            
        else:
            condition_where = (
                prod_hourly_data["ProductionLineStatus"] == "Running"
            )
            
        prod_hourly_data["running_duration"] = prod_hourly_data[
            "running_duration"
        ].where(condition_where, 0)

        
        prod_hourly_data["TotalFeed"] = pd.to_numeric(
            prod_hourly_data["TotalFeed"], errors="coerce"
        )
            
        prod_hourly_data["running_duration"] = pd.to_numeric(
            prod_hourly_data["running_duration"], errors="coerce"
        )

        prod_hourly_data["production"] = (
            prod_hourly_data["TotalFeed"]
            * prod_hourly_data["running_duration"]
        )

        prod_hourly_data["production"] = prod_hourly_data["production"] / 3600

        # replacing the production column for Newport Site

        if configurations.reporting_site.external_id in ["STS-NPT"]:
            # there is a different method of calculating hourly rate in Newport Site
            prod_hourly_data["production"] = prod_hourly_data["TotalFeed"].copy()

        # creating hourly data for sites with different shifts times
        list_shifts = configurations.shifts.copy()
        flag_classifier = False

        for date_str in list_shifts:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            if date_obj.minute == 30:
                flag_classifier = True
                break

        prod_hourly_data["index_utc"] = prod_hourly_data[
            "index"
        ].dt.tz_convert("UTC")

        if flag_classifier: # SHIFT BREAK IN 30 MINUTES
            prod_hourly_data["rounded_index"] = prod_hourly_data[
                "index_utc"
            ].dt.round("h")
            prod_hourly_data["start_time"] = prod_hourly_data[
                "rounded_index"
            ] - pd.Timedelta(minutes=30)
            mask = prod_hourly_data["index"].dt.minute == 30
            prod_hourly_data["start_time"] = prod_hourly_data[
                "start_time"
            ].dt.tz_convert(prod_hourly_data["index"].dt.tz)
            prod_hourly_data.loc[mask, "start_time"] = prod_hourly_data.loc[
                mask, "index"
            ].dt.floor("30min")

        else:
            prod_hourly_data["rounded_index"] = prod_hourly_data[
                "index_utc"
            ].dt.floor("h")
            prod_hourly_data["start_time"] = prod_hourly_data["rounded_index"]
            mask = prod_hourly_data["index"].dt.minute == 00
            prod_hourly_data["start_time"] = prod_hourly_data[
                "start_time"
            ].dt.tz_convert(prod_hourly_data["index"].dt.tz)

        prod_hourly_data.drop(
            columns=["index_utc", "rounded_index"], inplace=True
        )

        # there is a different method of calculating hourly rate in Newport Site
        if configurations.reporting_site.external_id in ["STS-NPT"]:
            prod_hourly_data = (
                prod_hourly_data.groupby(["start_time", "Product"])
                .agg(
                    {
                        "production": "first",
                        "running_duration": "sum",
                        "BatchID": "first",
                        "ProcessOrder": "first",
                        "ProductDescription": "first",
                    }
                )
                .reset_index()
            )

        else:
            prod_hourly_data = (
              prod_hourly_data.groupby(["start_time", "Product"])
              .agg(
                  {
                      "production": "sum",
                      "running_duration": "sum",
                      "BatchID": "first",
                      "ProcessOrder": "first",
                      "ProductDescription": "first",
                  }
              )
              .reset_index()
            )

        # if there are two products into an interval, it will get the most produced product
        prod_hourly_data = prod_hourly_data.loc[
            prod_hourly_data.groupby("start_time")["production"].idxmax()
        ]

        prod_hourly_data["end_time"] = pd.to_datetime(
            prod_hourly_data["start_time"]
        ) + pd.Timedelta(hours=1)

        prod_hourly_data["total_duration_seconds"] = (
            prod_hourly_data["end_time"] - prod_hourly_data["start_time"]
        ).dt.total_seconds()

        # find the year that is in accordance with the indexed date
        prod_hourly_data["Year"] = prod_hourly_data["start_time"].dt.year

        # find the month that is in accordance with the indexed date
        prod_hourly_data["Month"] = prod_hourly_data["start_time"].dt.month

        # PUT HERE THE EXTERNAL ID IF THE UNIT USE MDRTAG
        if configurations.external_id in {
            "OEERSC-STS-FLO",
            "OEERSC-STS-WIN",
            "OEERSC-STS-FOR",
            "OEERSC-STS-FRA",
            "OEERSC-STS-UTZ",
            "OEERSC-STS-PEN",
            "OEERSC-STS-NPT"
        }:
            if "MdrTag" not in prod_rate_data.columns:
                prod_rate_data["MdrTag"] = 0

            prod_hourly_data = pd.merge(
                prod_hourly_data,
                prod_rate_data.reset_index()[["index", "MdrTag"]],
                left_on="start_time",
                right_on="index",
                how="left",
            )

            prod_hourly_data.drop(columns=["index"], inplace=True)
            prod_hourly_data.rename(columns={"MdrTag": "MDR"}, inplace=True)
            
            unit_value = data_timeseries_configurations.loc[
                data_timeseries_configurations["alias"].str.contains("MdrTag", na=False),
                "unit"
            ].iloc[0]
            prod_hourly_data["mdr_unit"] = unit_value if unit_value is not None else "kg/h"
            
        else:
            prod_hourly_data["MDR"] = prod_hourly_data.apply(
                self._get_mdr_value, mdr_data=mdr, axis=1
            )
            prod_hourly_data["mdr_unit"] = "kg/h"
      
        # calculates hourly rate and rate loss time
        prod_hourly_data["hourly_rate"] = (
            prod_hourly_data["production"]
            / prod_hourly_data["running_duration"]
        ) * 3600

        prod_hourly_data.loc[
            prod_hourly_data["production"] == 0, "hourly_rate"
        ] = 0

        prod_hourly_data.loc[
            prod_hourly_data["running_duration"] == 0, "hourly_rate"
        ] = 0

        prod_hourly_data["rate_loss_time_h"] = (
            prod_hourly_data["MDR"] - prod_hourly_data["hourly_rate"]
        ) / prod_hourly_data["MDR"]

        prod_hourly_data.loc[
            prod_hourly_data["MDR"] == 0, "rate_loss_time_h"
        ] = 0

        prod_hourly_data.loc[
            prod_hourly_data["production"] == 0, "rate_loss_time_h"
        ] = 0

        prod_hourly_data["measurement_unit"] = (
            mdr["refUnitOfMeasurement"][0].get("symbol")
            if mdr is not None
            else ""
        )

        # look into the hourly data to get the events for
        # each working shift
        new_events = self._create_working_shifts(
            prod_hourly_data, configurations.shifts
        )

        # complete EventsIDs
        new_events["EventID"] = data["EventID"].max() + list(
            range(new_events.shape[0])
        )

        # concatenate events frames and new events
        data = pd.concat([data, new_events])

        # sort data
        data.sort_values(by=["end_time"], inplace=True)

        return data, prod_hourly_data

    def _create_working_shifts(
        self,
        data: pd.DataFrame,
        shifts: list[pd.Timestamp],
    ) -> pd.DataFrame:
        """
        looks into the hourly data information and creates the aggregation for each working shift

        :param data: hourly data dataframe
        :type data: pd.DataFrame
        :return: aggregate data for working shifts
        :rtype: pd.DataFrame
        """

        # New rule for FRACMP
        allowed_compounding_lines = [
            "RLN-FRACMP523", 
            "RLN-FRACMP528", 
            "RLN-FRACMP513", 
            "RLN-FRACMP522", 
            "RLN-FRACMP511", 
            "RLN-FRACMP525", 
            "RLN-FRACMP524", 
            "RLN-FRACMP526", 
            "RLN-FRACMP527", 
            "RLN-FRACMP521", 
            "RLN-FRACMP531"
        ]
        
        # get the nearest shift start concerning the first timestamp of
        # the hourly data

        start_time = data.iloc[0, :]["start_time"]

        morning_time, evening_time = self._get_min_max_date_shift(shifts)

        morning = (
            pd.Timestamp(
                start_time.strftime("%Y-%m-%d")
                + morning_time.strftime(" %H:%M:%S")
            )
        ).tz_localize(start_time.tz)
        evening = (
            pd.Timestamp(
                start_time.strftime("%Y-%m-%d")
                + evening_time.strftime(" %H:%M:%S")
            )
        ).tz_localize(start_time.tz)
        if start_time < morning:
            start_range = morning - pd.Timedelta(hours=12)
        elif (start_time >= morning) & (start_time < evening):
            start_range = morning
        else:
            start_range = evening

        # get the nearest shift start concerning the last timestamp of
        # the hourly data
        end_time = data.iloc[-1, :]["end_time"]
        morning = (
            pd.Timestamp(
                end_time.strftime("%Y-%m-%d")
                + morning_time.strftime(" %H:%M:%S")
            )
        ).tz_localize(start_time.tz)
        evening = (
            pd.Timestamp(
                end_time.strftime("%Y-%m-%d")
                + evening_time.strftime(" %H:%M:%S")
            )
        ).tz_localize(start_time.tz)

        if end_time < morning:
            end_range = morning
        elif (end_time >= morning) & (end_time < evening):
            end_range = evening
        else:
            end_range = evening + pd.Timedelta(hours=12)

        # create all working shifts dataframes
        ws_events = pd.DataFrame(
            pd.date_range(
                start=start_range,
                end=end_range - pd.Timedelta(hours=12),
                freq="12h",
            ),
            columns=["start_time"],
        )
        ws_events["end_time"] = pd.date_range(
            start=start_range + pd.Timedelta(hours=12),
            end=end_range,
            freq="12h",
        )

        # Correct start and end time of the working shifts when entering and leaving DST
        ws_events["tz_diff_start"] = ws_events.iloc[0]["start_time"].utcoffset() - ws_events["start_time"].apply(lambda x: x.utcoffset())
        ws_events["tz_diff_end"] = ws_events.iloc[0]["start_time"].utcoffset() - ws_events["end_time"].apply(lambda x: x.utcoffset())

        ws_events.loc[ws_events.iloc[0]["start_time"].dst() != ws_events["start_time"].apply(lambda x: x.dst()), "start_time"] += ws_events["tz_diff_start"]
        ws_events.loc[ws_events.iloc[0]["start_time"].dst() != ws_events["end_time"].apply(lambda x: x.dst()), "end_time"] += ws_events["tz_diff_end"]

        ws_events.drop(columns=["tz_diff_start", "tz_diff_end"], inplace=True)

        # aggregate hourly data into working shifts
        ws_events["total_duration_seconds"] = ws_events.apply(
            self._aggregate_hourly_data, hourly_data=data, axis=1
        )
        
        # aggregate production into working shifts       
        ws_events["NetProduction"] = ws_events.apply(
            self._aggregate_production, hourly_data=data, axis=1
        )
        
        # aggregate production into working shifts       
        ws_events["running_time"] = ws_events.apply(
            self._aggregate_running_duration, hourly_data=data, axis=1
        )
        
        if self._reporting_line_external_id in allowed_compounding_lines:
            ws_events = self._assign_event_definition(ws_events)
        else:
            # correct Production Events Descriptions
            ws_events["event_definition"] = ws_events[
                "total_duration_seconds"
            ].apply(lambda x: "3a" if x > 0 else ("3b" if x < 0 else None))

        # drop shift events with duration zero (event definition = None)
        ws_events.dropna(inplace=True)

        ws_events = pd.merge(
            ws_events,
            data[
                [
                    "start_time",
                    "Product",
                    "ProductDescription",
                    "BatchID",
                    "ProcessOrder",
                    "MDR",
                    "mdr_unit",
                ]
            ],
            on="start_time",
        )

        return ws_events

    def _assign_event_definition(self, ws_events: pd.DataFrame):
        """
        This function receives a DataFrame ws_events and creates the 'event_definition' column based on the
        'total_duration_seconds' column according to the following rules:

        - If the value is between 0 (exclusive) and 15 minutes (inclusive): '3a'
        - If the value is between -15 minutes (inclusive) and 0 (exclusive): '3b'
        - If the value is positive and greater than 15 minutes: '3c'
        - If the value is negative and less than -15 minutes: '3d'

        Note: total_duration_seconds is in seconds, so the thresholds are in seconds (15 minutes = 900 seconds).
        Values that do not meet any of these conditions will receive the default value (None).
        """
        # Convert 15 minutes to seconds
        minutes_to_seconds = 15 * 60  # 900 seconds
        
        cond_3a = (ws_events['total_duration_seconds'] > 0) & (ws_events['total_duration_seconds'] <= minutes_to_seconds)
        
        cond_3b = (ws_events['total_duration_seconds'] < 0) & (ws_events['total_duration_seconds'] >= -minutes_to_seconds)
    
        cond_3c = (ws_events['total_duration_seconds'] > minutes_to_seconds)
        
        cond_3d = (ws_events['total_duration_seconds'] < -minutes_to_seconds)
        
        conditions = [cond_3a, cond_3b, cond_3c, cond_3d]
        choices = ['3a', '3b', '3c', '3d']
        
        ws_events['event_definition'] = np.select(conditions, choices, default=None)
        
        return ws_events

    def _aggregate_hourly_data(
        self, row: pd.DataFrame, hourly_data: pd.DataFrame
    ) -> float:
        """
        aggregates the rate loss time for the entire working shift

        :param row: row with the start and end time of the working shifts
        :type row: pd.DataFrame
        :param hourly_data: data with hourly rate loss time
        :type hourly_data: pd.DataFrame
        :return: aggregation of the rate loss time for each working shift
        :rtype: float
        """

        # get start and end of the shift
        start = row["start_time"]
        end = row["end_time"]

        # filter the hourly data and sum the rate loss time
        rlt = hourly_data.loc[
            (
                (hourly_data["start_time"] >= start)
                & (hourly_data["end_time"] <= end)
            ),
            "rate_loss_time_h",
        ].sum()

        return rlt * 3600
    
    def _aggregate_production(
        self, row: pd.Series, hourly_data: pd.DataFrame
    ) -> float:
        """
        Aggregates the production for the entire working shift.

        :param row: row with the start and end time of the working shift
        :type row: pd.Series
        :param hourly_data: data with hourly production values
        :type hourly_data: pd.DataFrame
        :return: total production for the shift
        :rtype: float
        """
        start = row["start_time"]
        end = row["end_time"]

        production_sum = hourly_data.loc[
            (hourly_data["start_time"] >= start) & (hourly_data["end_time"] <= end),
            "production"
        ].sum()

        return production_sum
    
    def _aggregate_running_duration(
        self, row: pd.Series, hourly_data: pd.DataFrame
    ) -> float:
        """
        Aggregates the running duration for the entire working shift.

        :param row: row with the start and end time of the working shift
        :type row: pd.Series
        :param hourly_data: data with hourly running duration values
        :type hourly_data: pd.DataFrame
        :return: total running duration for the shift
        :rtype: float
        """
        start = row["start_time"]
        end = row["end_time"]

        production_sum = hourly_data.loc[
            (hourly_data["start_time"] >= start) & (hourly_data["end_time"] <= end),
            "running_duration"
        ].sum()

        return production_sum
    
    def _get_mdr_value(self, row: pd.Series, mdr_data: pd.DataFrame) -> float:
        """
        retrieves the MDR from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: MDR value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["Product"]
        mdr_product = self.__get_mdr_product(mdr_data=mdr_data)
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (mdr_product == prod_id)
            & (["reportingLineExternalId"] == self._reporting_line_external_id)
            & (mdr_data["year"] == start_time_year)
            & (mdr_data["month"] == start_time_month)
        )
        aux = mdr_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (
                mdr_product.str.lower() == prod_id.lower()
            ) & (
                mdr_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = mdr_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.ffill(inplace=True)
            aux.fillna({"unitAvgRate": 0}, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux["unitAvgRate"].head(1).values[0]

    def _get_min_max_date_shift(self, shifts):
        filtered_shifts = shifts.copy()

        datetime_objects = [
            datetime.strptime(ts, "%Y-%m-%d %H:%M:%S") for ts in shifts
        ]

        # get 00:00:00 time to remove of shifts list
        min_datetime = min(datetime_objects, key=lambda dt: dt.time())

        # transform to string again
        min_str_datetime = min_datetime.strftime("%Y-%m-%d %H:%M:%S")

        filtered_shifts.remove(min_str_datetime)

        all_shifts_datetime = [
            datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")
            for ts in filtered_shifts
        ]

        # get min shift date
        min_shift_date = min(all_shifts_datetime, key=lambda dt: dt.time())

        # get max shift date
        max_shift_date = max(all_shifts_datetime, key=lambda dt: dt.time())

        return min_shift_date, max_shift_date
    
    def __get_mdr_product(self, mdr_data: pd.DataFrame):
        material = mdr_data["refMaterial"]
        
        if not material.empty:
            return pd.Series(
                [
                    item["name"]
                    for item in material if isinstance(item, dict)
                
                ]
            )
        
        return mdr_data["productId"]