from datetime import time
from typing import Any, Optional

import numpy as np
import pandas as pd
from app.enums.clk.valves import GuardBedInlet, ProductToV5331
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value


class CLKAANEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "3e": self.identify_events_typeIIIe,
            "3f": self.identify_events_typeIIIf,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "dt"
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        data["dt"] = data["dt"] / 60

        data["ProductionLineStatus_1_duration"] = data.groupby(
            (
                (data["ProductionLineStatus_1"].shift(1) > 1)
                & (data["ProductionLineStatus_1"] <= 1)
            ).cumsum()
        )["dt"].cumsum()

        data["ProductionLineStatus_2_duration_start"] = data.groupby(
            (
                (data["ProductionLineStatus_2"].shift(1) > 0)
                & (data["ProductionLineStatus_2"] == 0)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus_1"] > 1, "ProductionLineStatus_1_duration"] = 0
        data.loc[data["ProductionLineStatus_2"] > 0, "ProductionLineStatus_2_duration_start"] = 0

        # event trigger start - (ProductionLineStatus_1 <= 1
        # AND ProductionLineStatus_2 = 0) during 15 minutes
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus_1"] <= 1)
                & (data["ProductionLineStatus_2"] == 0)
                & (data["ProductionLineStatus_1_duration"] >= 15)
                & (data["ProductionLineStatus_2_duration_start"] >= 15)
            )
        )

        data["ProductionLineStatus_2_duration_end"] = data.groupby(
            (
                (data["ProductionLineStatus_2"].shift(1) <= 5)
                & (data["ProductionLineStatus_2"] > 5)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus_2"] <= 5, "ProductionLineStatus_2_duration_end"] = 0

        # event end - (ProductionLineStatus_1 <= 1
        # AND ProductionLineStatus_2 > 5%) during 15 minutes
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus_1"] <= 1)
                & (data["ProductionLineStatus_2"] > 5)
                & (data["ProductionLineStatus_1_duration"] >= 15)
                & (data["ProductionLineStatus_2_duration_end"] >= 15)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "GuardBedInlet",
            "ProductToV5331",
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        # create dt column in minutes
        data.set_index("index", inplace=True)
        data["dt"] = data.index.to_series().diff().dt.total_seconds().fillna(0)
        data["dt"] = data["dt"] / 60
        data.reset_index(inplace=True)

        data["ProductionLineStatus_1_duration"] = data.groupby(
            (
                (data["ProductionLineStatus_1"].shift(1) > 1)
                & (data["ProductionLineStatus_1"] <= 1)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus_1"] > 1, "ProductionLineStatus_1_duration"] = 0

        data["ProductionLineStatus_2_duration_start"] = data.groupby(
            (
                (data["ProductionLineStatus_2"].shift(1) <= 5)
                & (data["ProductionLineStatus_2"] > 5)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus_2"] <= 5, "ProductionLineStatus_2_duration_start"] = 0

        data["ProductionLineStatus_2_duration_end"] = data.groupby(
            (
                (data["ProductionLineStatus_2"].shift(1) > 0)
                & (data["ProductionLineStatus_2"] == 0)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["ProductionLineStatus_2"] > 0, "ProductionLineStatus_2_duration_end"] = 0

        # event trigger start - (ProductionLineStatus_1 <= 1
        # AND ProductionLineStatus_2 > 5%) during 15 minutes
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_1"] <= 1)
                & (data["ProductionLineStatus_1_duration"] >= 15)
                & (data["ProductionLineStatus_2"] > 5)
                & (data["ProductionLineStatus_2_duration_start"] >= 15)
            )
        )

        # calculate the duration
        data["guard_bed_inlet_duration"] = data.groupby(
            (data["GuardBedInlet"] != data["GuardBedInlet"].shift()).cumsum()
        )["dt"].cumsum()

        # event end - ProductionLineStatus_1 > 100
        # AND (GuardBedInlet = OPENED for 45min)
        # AND ProductToV5331 != OPENED
        # OR (ProductionLineStatus_1 <= 1 
        # AND ProductionLineStatus_2 = 0) during 15 minutes
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus_1"] > 100)
                    & (data["GuardBedInlet"] == GuardBedInlet.OPENED.value)
                    & (data["ProductToV5331"] != ProductToV5331.OPENED.value)
                    & (data["guard_bed_inlet_duration"] >= 45)
                )
                | (
                    (data["ProductionLineStatus_1"] <= 1)
                    & (data["ProductionLineStatus_2"] == 0)
                    & (data["ProductionLineStatus_1_duration"] >= 15)
                    & (data["ProductionLineStatus_2_duration_end"] >= 15)
                )
            )
        )

        # remove temp columns
        data.drop(["dt", "guard_bed_inlet_duration", "ProductionLineStatus_1_duration", "ProductionLineStatus_2_duration_start", "ProductionLineStatus_2_duration_end"], axis=1, inplace=True)

        # correct start and end flags
        data = data.assign(
            event2a_start=(
                (data["event2a_start"] == True)
                & (data["event2a_start"].shift(1) != True)
            )
        ).assign(
            event2a_end=(
                (data["event2a_end"] == True)
                & (data["event2a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        if data.empty:
            return data

        data["RLT"] = (
            ((data["ScheduledRateYield"] * data["running_time"] / 24) - data["TotalFeed"]) / (
                data["ScheduledRateYield"] / 24
            )
        )

        # MSDP > SR and SR > TotalFeed
        data = data.assign(
            event3a_end=(
                (data["msdp"] > data["ScheduledRateYield"])
                & (data["running_time"] > 0.1)
                & (data["ScheduledRateYield"] > data["TotalFeed"])
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3a_start=(data["event3a_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (
            (data["msdp"] - data["ScheduledRateYield"]) / (
                data["msdp"] / 24
            )
        )

        # MSDP > SR and SR > TotalFeed
        data = data.assign(
            event3b_end=(
                (data["msdp"] > data["ScheduledRateYield"])
                & (data["running_time"] > 0.1)
                & (data["ScheduledRateYield"] > data["TotalFeed"])
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (data["msdp"] - data["TotalFeed"]) / (
            data["msdp"] / 24
        )

        # MSDP > TotalFeed and TotalFeed > SR
        data = data.assign(
            event3c_end=(
                (data["msdp"] > data["TotalFeed"])
                & (data["running_time"] > 0.1)
                & (data["TotalFeed"] > data["ScheduledRateYield"])
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3c_start=(data["event3c_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (
            ((data["msdp"] * data["running_time"] / 24) - data["TotalFeed"]) / (
                data["msdp"] / 24
            )
        )

        # TotalFeed > MSDP
        data = data.assign(
            event3d_end=(
                (data["TotalFeed"] > data["msdp"])
                & (data["running_time"] > 0.1)
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3d_start=(data["event3d_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data
    
    def identify_events_typeIIIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIe

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (
            ((data["ScheduledRateYield"] * data["running_time"] / 24) - data["TotalFeed"]) / (
                data["ScheduledRateYield"] / 24
            )
        )

        # MSDP == SR and SR > TotalFeed
        data = data.assign(
            event3e_end=(
                (data["msdp"] == data["ScheduledRateYield"])
                & (data["running_time"] > 0.1)
                & (data["ScheduledRateYield"] > data["TotalFeed"])
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3e_start=(data["event3e_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data
    
    def identify_events_typeIIIf(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIf

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (
            (data["msdp"] - data["ScheduledRateYield"]) / (
                data["msdp"] / 24
            )
        )

        # Previous day (MSDP > SR and SR = TotalFeed)
        data = data.assign(
            event3f_end=(
                (data["msdp"].shift(1) > data["ScheduledRateYield"].shift(1))
                & (data["running_time"] > 0.1)
                & (data["ScheduledRateYield"].shift(1) == data["TotalFeed"].shift(1))
                & (data["TotalFeed"] > 0)
                & (data["TotalFeed"] != data["msdp"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3f_start=(data["event3f_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b, 3c, 3d, 3e and 3f

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if self._day_data is not None:
            return self._day_data.copy()
        
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data

        data.set_index("index", inplace=True)
        data["dt"] = data.index.to_series().diff().dt.total_seconds().fillna(0)

        LBS_TO_MT_DIVISION_FACTOR = 2204.62

        # Convert from pounds to metric tonne
        data["TotalFeed"] = data["TotalFeed"] / LBS_TO_MT_DIVISION_FACTOR
        data["ScheduledRateYield"] = data["ScheduledRateYield"] / LBS_TO_MT_DIVISION_FACTOR

        # Convert from daily to hourly measurement
        data["TotalFeed"] = data["TotalFeed"] / 24
        data["ScheduledRateYield"] = data["ScheduledRateYield"] / 24

        data["TotalFeed"] = (data["TotalFeed"] / 3600) * data["dt"]
        data.reset_index(inplace=True)

        running_time_key = "running_time"

        # create dt column in minutes
        data["dt_minutes"] = data["dt"] / 60
        data.reset_index(drop=True, inplace=True)

        # calculate the duration
        data["guard_bed_inlet_duration"] = data.groupby(
            (data["GuardBedInlet"] != data["GuardBedInlet"].shift()).cumsum()
        )["dt_minutes"].cumsum()

        # Modified running_time calculation
        running_condition = (
            (data["ProductionLineStatus_1"] > 100)
            & (data["GuardBedInlet"] == GuardBedInlet.OPENED.value)
            & (data["ProductToV5331"] != ProductToV5331.OPENED.value)
            & (data["guard_bed_inlet_duration"] >= 45)
        )

        not_running_condition = (~running_condition)

        data["condition_running_time"] = not_running_condition

        data["duration_in_seconds_without_conditions"] = (data["index"].diff().dt.total_seconds().fillna(0))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                "ScheduledRateYield": "first",
                "TotalFeed": "sum",
                "duration_in_seconds": "sum",
                "ProductionLineStatus_1": "mean",
            }
        )

        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})
        
        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        msdp_data = self._msdp.loc[
            (
                self._msdp["reportingLineExternalId"]
                == self._reporting_line_external_id
            ),
            :,
        ]

        data["msdp"] = data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )

        data["msdp"] = data["msdp"] / 24

        # If Total Production != 0 and Scheduled Rate = 0, replace with most recent non-zero Scheduled Rate
        # If there is no non-zero Scheduled Rate, replace with MSDP
        valid_sr = (
            (data["ScheduledRateYield"].notna()) 
            & (data["ScheduledRateYield"] != 0)
        )
        condition_replace_sr = not data[valid_sr].empty

        if condition_replace_sr:
            scheduled_rate_replace = data.loc[valid_sr, "ScheduledRateYield"].iloc[-1]
        else:
            scheduled_rate_replace = data["msdp"]

        data.loc[(
            (
                (data["TotalFeed"] != 0) 
                & (data["ScheduledRateYield"] == 0)
            ) 
            | (
                data["ScheduledRateYield"].isna()
            )
        ), "ScheduledRateYield"] = scheduled_rate_replace

        data = (
            data.groupby(
                [pd.Grouper(key="timestamp", freq="D"), data["ProductDescription"]],
            )
            .agg(
                {
                    "msdp": "sum",
                    "ScheduledRateYield": "sum",
                    "TotalFeed": "sum",
                    "running_time": "sum",
                },
            )
            .reset_index()
        )

        data["is_noon"] = (
            (data["timestamp"].dt.hour == 00)
            & (data["timestamp"].dt.minute == 0)
            & (data["timestamp"].dt.second == 0)
            & (data["timestamp"].dt.microsecond == 0)
        )
        
        data["TotalFeed"] = data["TotalFeed"].shift(-1)


        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["ProductDescription"]
        )

        self._day_data = data

        return data
    
    def get_msdp_value(
        self,
        row: pd.Series,
        msdp_data: pd.DataFrame,
        data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract without product filtering
        """
        return utils_get_msdp_value(row, msdp_data, data_aux)

    def get_inactive_value(self) -> Optional[str]:
        return None

    def __remove_unecessary_data(
        self, data: pd.DataFrame, columns_to_keep: list[str]
    ) -> pd.DataFrame:
        remove_columns = [
            col
            for col in data.columns.to_list()
            if col not in columns_to_keep + ["index"]
        ]
        return data.drop(remove_columns, axis=1).dropna(
            subset=columns_to_keep, how="all"
        )