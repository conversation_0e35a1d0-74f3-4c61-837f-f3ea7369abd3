from app.models.reporting_site_configuration import ReportingSiteConfiguration
from typing import Any, Optional
import re
import numpy as np
import pandas as pd
from app.enums.inactive_value_enum import InactiveValueEnum

class BchBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }
    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods    

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies the events of type Ia.

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
 
        event1a_start = (data["ProductionLineStatus"] == "Inactive") & (data["ProductionLineStatus"].shift(1) != "Inactive")

        event1a_end = (data["ProductionLineStatus"] != "Inactive") & (data["ProductionLineStatus"].shift(1) == "Inactive")

        data = data.assign(
            event1a_start=event1a_start,
            event1a_end=event1a_end
        )

        return data
    def identify_events_typeIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        Identifies events of type Ib.
        """
        correct_values = self.find_strings_in_column_values(data, "BatchHold", ["Held"])

        event1b_start = correct_values & (~correct_values.shift(1, fill_value=False))

        event1b_end = ~correct_values & (correct_values.shift(1, fill_value=False))

        data = data.assign(
            event1b_start=event1b_start,
            event1b_end=event1b_end
        )

        return data    
    

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events of type Ic.
        
        
        """
        correct_values = self.find_strings_in_column_values(data, "BatchHold", ["Stopping", "Stopped", "Stop", "Abort"])

        event1c_start = correct_values & (~correct_values.shift(1, fill_value=False))

        event1c_end = ~correct_values & (correct_values.shift(1, fill_value=False))
        data = data.assign(
            event1c_start=event1c_start,
            event1c_end=event1c_end
        )

        return data
    

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events of type IIIb.
        
        
        """
        # event trigger start - ProductionLineStatus prefix = UP_
        data = data.assign(
            event3b_start=(
                data["ProductionLineStatus"].str.startswith("UP_") &
                ~data["ProductionLineStatus"].str.startswith("UP_").shift(1).fillna(False)
            )
        )
        data = data.assign(
            event3b_start=(
                (data["event3b_start"] == True)
                & (data["event3b_start"].shift(1) != True)
            )
        )

        data = data.assign(
            event3b_end=(
                (data["ProductionLineStatus"] == "Inactive") & (data["ProductionLineStatus"].shift(1) != "Inactive")
            )
        )
        data = data.assign(
            event3b_end=(
                (data["event3b_end"] == True)
                & (data["event3b_end"].shift(1) != True)
            )
        )
      
        return data
    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass
    
    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> float:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["ProductDescription"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0

        # first filter - filter by year and month of the start date and
        # by the productid getting the first 4 digits
        prefix_prod_id = prod_id[:4]

        filter_1 = (
               (bbct_data["productId"].str.lower().str[:4] == prefix_prod_id)
            & (bbct_data["reportingLineExternalId"] == self._reporting_line_external_id)
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (bbct_data["productId"].str.lower() == prod_id) & (
                bbct_data["reportingLineExternalId"] == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux["bbct"].head(1).values[0]    
    
    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            self.get_bbct_value,
            bbct_data=bbct_data,
            axis=1,
        )
        
        # fix events 3b duration
        data["total_duration_seconds"] -= data["bbct"]

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # events which products does not have bbct is reported with duration zero
        data.loc[
            ((data["event_definition"] == "3b") & (data["bbct"] == 0)),
            "total_duration_seconds",
        ] = 0

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data
    
    def find_strings_in_column_values(self, data: pd.DataFrame, column_name: str, substrings: list) -> pd.Series:
        """
        Checks if any of the specified substrings are present in the specified column.

        :param data: The DataFrame containing the data.
        :param column_name: The name of the column to check.
        :param substrings: A list of substrings to search for.
        :return: A boolean Series indicating where any of the substrings are present.
        """
        if column_name not in data.columns:
            raise ValueError(f"Column '{column_name}' does not exist in the DataFrame.")

        # Create a regex pattern from the substrings list and compile it
        pattern = '|'.join([re.escape(substring) for substring in substrings])
        regex = re.compile(pattern, re.IGNORECASE)

        # Use numpy vectorization to apply the regex pattern
        contains_string = np.vectorize(lambda x: bool(regex.search(x) if pd.notnull(x) else False))(data[column_name].values)

        return pd.Series(contains_string, index=data.index)
            
    def get_inactive_value(self) -> Optional[str]:
        return None            
