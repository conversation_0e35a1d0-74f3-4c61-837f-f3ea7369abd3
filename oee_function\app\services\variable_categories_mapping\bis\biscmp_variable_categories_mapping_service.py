class BisCmpVariableCategoriesMappingService:
    def __init__(self) -> None:
        self._mapping_subcatlevel2_dict = {
            0: "",
            1: "",
            2: "",
            3: "",
            4: "",
            5: "",
            6: "",
            7: "",
            8: "",
            9: "",
            10: "",
            11: "",
            12: "",
            13: "Blade Rap",
            14: "Torque Out",
            15: "",
            16: "",
            17: "",
        }
        
        self._mapping_subcatlevel1_dict = {
            0: "Running",
            1: "Unassigned",
            2: "Vacuum",
            3: "Turnarounds",
            4: "",
            5: "",
            6: "Quality issue",
            7: "External Raw Material",
            8: "No Demand",
            9: "Packaging",
            10: "Waiting on Test Result",
            11: "",
            12: "Packaging",
            13: "Quench / Cutter / Water Removal",
            14: "Extruder",
            15: "Manpower",
            16: "Scrap",
            17: "Manpower",
        }

        self._mapping_eventcode_dict = {
            0: "",
            1: "Unassigned",
            2: "Reactive Downtime",
            3: "Turnarounds",
            4: "Reactive Downtime",
            5: "Reactive Downtime",
            6: "Reactive Downtime",
            7: "Product And Supply Optimization",
            8: "No Demand",
            9: "Reactive Downtime",
            10: "Reactive Downtime",
            11: "Planned Downtime",
            12: "Reactive Downtime",
            13: "Planned Downtime",
            14: "Planned Downtime",
            15: "Reactive Downtime",
            16: "Scrap",
            17: "Reactive Downtime",
        }

        self._mapping_metriccode_dict = {
            0: "",
            1: "Availability",
            2: "Availability",
            3: "Availability",
            4: "Availability",
            5: "Availability",
            6: "Availability",
            7: "Availability",
            8: "Loading",
            9: "Availability",
            10: "Availability",
            11: "Availability",
            12: "Availability",
            13: "Availability",
            14: "Availability",
            15: "Availability",
            16: "Quality",
            17: "Availability",
        }

    def map(self, prod_line_status: int, property: str) -> str:
        property_dict_mapping = {
            "subCategoryLevel2": self._mapping_subcatlevel2_dict,
            "subCategoryLevel1": self._mapping_subcatlevel1_dict,
            "eventCode": self._mapping_eventcode_dict,
            "metricCode": self._mapping_metriccode_dict,
        }
        property_dict = property_dict_mapping.get(property)
        return property_dict.get(prod_line_status)
