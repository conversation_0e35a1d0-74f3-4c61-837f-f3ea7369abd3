from typing import Any, Optional, Protocol

import pandas as pd


class EventIdentification(Protocol):
    def get_events_identification_methods(self) -> dict[str, Any]:
        pass

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass
    
    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass
    
    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass
    
    def identify_events_typeIIIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        pass
    
    def fix_events_type4_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        pass

    def implement_RLT(
        self, data: pd.DataFrame, data_cdf: pd.DataFrame
    ) -> pd.DataFrame:
        pass

    def get_inactive_value(self) -> Optional[str]:
        pass
