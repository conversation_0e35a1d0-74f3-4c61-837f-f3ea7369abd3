SELECT
  (CASE Order 
  	WHEN 700206 THEN "OEE:BISCHM-MO3:NetProduction"
  	WHEN 700205 THEN "OEE:BISCHM-MO4:NetProduction"
  	WHEN 700161 THEN "OEE:BISCHM-PFM:NetProduction"
  END) AS externalId,
  to_timestamp(`Pstng Date`, 'MM/dd/yyyy') AS timestamp,
  max(cast(replace(`Quantity in UnE`,',', '') AS double)) AS value
FROM
  `SAP-COR`.`OEE-MB51-DAILY`
WHERE
  Plnt = 2002
  AND (
  	Order = 700206 
  	OR Order = 700205 
  	OR Order = 700161
) 
AND NOT (cast(replace(`Quantity in UnE`,',', '') AS double)) is null
GROUP BY
  externalId,
  timestamp
  ;