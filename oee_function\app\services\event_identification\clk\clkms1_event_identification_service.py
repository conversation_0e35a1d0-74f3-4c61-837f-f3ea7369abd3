from datetime import time
from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class CLKMS1EventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._day_data = None

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "V9302CrudeMeOHOutletFlow"
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        # event trigger start - ProductionLineStatus_1 < 10
        # AND ProductionLineStatus_2 < 10
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus_1"] < 10)
                & (data["ProductionLineStatus_2"] < 10)
            )
        )

        # event end - ProductionLineStatus_1 > 10
        # OR ProductionLineStatus_2 > 10
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus_1"] > 10)
                & (data["ProductionLineStatus_2"] > 10)
                & (data["V9302CrudeMeOHOutletFlow"] < 10)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        identification_columns = [
            "ProductionLineStatus_1",
            "ProductionLineStatus_2",
            "V9302CrudeMeOHOutletFlow",
        ]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        # event trigger start - (ProductionLineStatus_1 + ProductionLineStatus_2) > 10
        # AND V9302CrudeMeOHOutletFlow < 10
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_1"] > 10) 
                & ( data["ProductionLineStatus_2"] > 10)
                & (data["V9302CrudeMeOHOutletFlow"] < 10)
            )
        )

        # event end - (ProductionLineStatus_1 + ProductionLineStatus_2) > 10
        # AND V9302CrudeMeOHOutletFlow > 10
        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus_1"] > 10) & ( data["ProductionLineStatus_2"] > 10)
                )
                & (data["V9302CrudeMeOHOutletFlow"] > 10)
            )
        )

        # correct start and end flags
        data = data.assign(
            event2a_start=(
                (data["event2a_start"] == True)
                & (data["event2a_start"].shift(1) != True)
            )
        ).assign(
            event2a_end=(
                (data["event2a_end"] == True)
                & (data["event2a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data=data, col="NetProduction")

        data["RLT"] = ((data["MSDP"] * data["running_time"] / 24) - data["NetProduction"]) / (
            data["MSDP"] / 24
        )

        data = data.assign(
            event3a_end=(
                (data["RLT"] > 0)
                & (data["running_time"] > 0.1)
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3a_start=(data["event3a_end"] == True)
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data=data, col="NetProduction")

        data["RLT"] = ((data["MSDP"] * data["running_time"] / 24) - data["NetProduction"]) / (
            data["MSDP"] / 24
        )

        data = data.assign(
            event3b_end=(
                (data["RLT"] < 0)
                 & (data["running_time"] > 0.1)
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_noon"])
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"] == True)
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        # remove unecessary data
        # identification_columns = ["WasteTotalizer"]
        # data = self.__remove_unecessary_data(
        #     data=data, columns_to_keep=identification_columns
        # )

        # data.loc[data["WasteTotalizer"] < 0, "WasteTotalizer"] = 0

        # data = self.__integrate_waste_totalizer(data=data)

        # data = self.__create_day_data(data=data, col="MeOHWasted")

        # data["RLT"] = (data["MeOHWasted"]) / (data["MSDP"] / 24)

        data = data.assign(event4a_end=False)

        data = data.assign(
            event4a_start=False
        )

        # data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
        #     subset=["total_duration_seconds"]
        # )

        return data

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame, col: str) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()
        
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        # start_cutoff = (
        #     None
        #     if first_timestamp.time() == mid_night_time
        #     else first_timestamp.date()
        # )
        # end_cutoff = last_timestamp.date()

        # # remove partial days from start and end
        # data = data[
        #     (
        #         (start_cutoff is not None)
        #         & (data["index"].dt.date > start_cutoff)
        #     )
        #     & ((end_cutoff is not None) & (data["index"].dt.date <= end_cutoff))
        # ]
        

        # define horário da meia-noite
        mid_night_time = time(0, 0, 0, 0)

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]

        # define corte do primeiro dia, se ele não começar à meia-noite
        start_cutoff = (
            None if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )

        # define a data do último dia
        last_day = last_timestamp.date()

        # extrai apenas o registro do último dia que for exatamente 00:00:00
        midnight_last_day = data[
            (data["index"].dt.date == last_day)
            & (data["index"].dt.time == mid_night_time)
        ]

        # aplica o corte de dados:
        # - remove o primeiro dia parcial
        # - remove o último dia inteiro
        data_filtered = data[
            ((start_cutoff is None) | (data["index"].dt.date > start_cutoff)) &
            (data["index"].dt.date < last_day)
        ]

        # junta com o registro 00:00:00 do último dia, se ele existir
        if not midnight_last_day.empty:
            data = pd.concat([data_filtered, midnight_last_day], ignore_index=True)
            data = data.sort_values("index").reset_index(drop=True)
        else:
            data = data_filtered


        if data.empty:
            return data

        data.set_index("index", inplace=True)

        LBS_TO_MT_DIVISION_FACTOR = 2204.62

        data[col] = data[col] / LBS_TO_MT_DIVISION_FACTOR

        # data[col] = (data[col] / 3600) * data["dt"]
        data.reset_index(inplace=True)

        running_time_key = "running_time"

        # Modified running_time calculation
        not_running_condition = (
            (data["ProductionLineStatus_1"] < 10)
            & (data["ProductionLineStatus_2"] < 10)
        )
        
        data["condition_running_time"] = not_running_condition

        data["duration_in_seconds_without_conditions"] = (data["index"].diff().dt.total_seconds().fillna(0))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                col: "first",
                "duration_in_seconds": "sum",
                "ProductionLineStatus_1": "mean",
            }
        )

         # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})
        
        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )
        
        data = (
            data.groupby(
                [pd.Grouper(key="timestamp", freq="D"), data["ProductDescription"]],
            )
            .agg(
                {
                    "MSDP": "first",
                    "SCHR": "first",
                    col: "first",
                    "running_time": "sum",
                },
            )
            .reset_index()
        )

        data["is_noon"] = (
            (data["timestamp"].dt.hour == 0)
            & (data["timestamp"].dt.minute == 0)
            & (data["timestamp"].dt.second == 0)
            & (data["timestamp"].dt.microsecond == 0)
        )
        
        if col == "NetProduction":
            data[col] = data[col].shift(-1)
          

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["ProductDescription"]
        )

        self._day_data = data

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def get_inactive_value(self) -> Optional[str]:
        return None

    def __remove_unecessary_data(
        self, data: pd.DataFrame, columns_to_keep: list[str]
    ) -> pd.DataFrame:
        remove_columns = [
            col
            for col in data.columns.to_list()
            if col not in columns_to_keep + ["index"]
        ]
        return data.drop(remove_columns, axis=1).dropna(
            subset=columns_to_keep, how="all"
        )

    def __integrate_waste_totalizer(self, data: pd.DataFrame) -> pd.DataFrame:
        data["dt"] = data["index"].diff(-1).dt.total_seconds().abs()
        data["wasted_mlbs"] = data["WasteTotalizer"] * (data["dt"] / 60)
        data.set_index("index", inplace=True)
        data["wasted_mlbs_sum"] = (
            data["wasted_mlbs"].rolling(window="24h").sum()
        )
        data["MeOHWasted"] = data["wasted_mlbs_sum"] * 1000
        data.reset_index(inplace=True)

        # remove temp columns
        data.drop(
            ["dt", "wasted_mlbs", "wasted_mlbs_sum"],
            axis=1,
            inplace=True,
        )
        return data
