from datetime import datetime
from typing import Optional

from cognite.client import CogniteClient

from app.infra.env_variables import EnvVariables
from app.infra.logger_adapter import get_logger
from app.infra.services_factory import create_event_frame_service

log = get_logger()


def handle(
    client: CogniteClient = None,
    data: dict = None,
    secrets: dict = None,
    function_call_info: dict = None,
) -> None:
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    if "start_time" in data:
        start_time = datetime.fromisoformat(data["start_time"])

    if "end_time" in data:
        end_time = datetime.fromisoformat(data["end_time"])


    log.info("Starting OEE event frames")

    variables = EnvVariables()
    log.info(f"Cognite configs: {variables.cognite.model_dump()}")
    service = create_event_frame_service(variables)
    service.transform_time_series_to_event_frames(
        start_time=start_time,
        end_time=end_time,
        reporting_site_external_id=str(data["site_external_id"]),
        only_lines=data.get("only_lines", None),
    )

    log.info("PI tags successfully transformed into OEE event frames")


if __name__ == "__main__":
    handle(data={
    "only_lines": [
        "RLN-BAYVAMVAM"
    ],
    "site_external_id": "STS-BAY"
   })
