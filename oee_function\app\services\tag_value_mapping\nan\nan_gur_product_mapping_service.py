import pandas as pd

class NanGurProductMappingService:
    def __init__(self) -> None:
        self._mapping_dict = {
          "4011": "51032486",
          "4012F": "51031615",
          "4012P": "51014585",
          "4016P": "51014562",
          "4018": "51032524",
          "4018P": "51009213",
          "4022": "50006595",
          "4022S": "51014458",
          "4032": "50006597",
          "4112F": "51031617",
          "4116": "51014904"
        }

    def map(self, data: pd.DataFrame, time_series: str) -> pd.DataFrame:
        # Remove single or multiple spaces using regex
        data["ProductDescription"] = data["ProductDescription"].str.replace(r'\s+', '', regex=True)
        data["Product"] = data["Product"].str.replace(r'\s+', '', regex=True)
        return data.replace({time_series: self._mapping_dict})
