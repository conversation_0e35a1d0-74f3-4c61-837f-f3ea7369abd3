from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class NarContinuousEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus = 0
        data = data.assign(event1a_start=(data["ProductionLineStatus"] == 0))

        # event trigger end - ProductionLineStatus > 0
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus"] > 0)
                & (data["event1a_start"].shift(1) == 1)
            )
        )

        # start flag correction
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] == 0)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """

        # identify changes from status

        silo_conditions_start = []
        for i in range(1, 9):
            silo_conditions_start.append(
                (data[f"ProductionLineDisposition_{i}"] != 1)  # Silo 1
                & (data[f"ProductionLineDisposition_{i}"] != 2)  # Silo 2
                & (data[f"ProductionLineDisposition_{i}"] != 3)  # Silo 3
                & (data[f"ProductionLineDisposition_{i}"] != 4)  # Silo 4
                & (data[f"ProductionLineDisposition_{i}"] != 5)  # Silo 5
            )

        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] > 0)
                & (
                    (silo_conditions_start[0])  # Position 1
                    & (silo_conditions_start[1])  # Position 2
                    & (silo_conditions_start[2])  # Position 3
                    & (silo_conditions_start[3])  # Position 4
                    & (silo_conditions_start[4])  # Position 5
                    & (silo_conditions_start[5])  # Position 6
                    & (silo_conditions_start[6])  # Position 7
                    & (silo_conditions_start[7])  # Position 8
                )
            )
        )

        # identify changes from status again to define end event ("Any of 8 Prod Line Disposition = 1 or 2 or 3 or 4 or 5")
        silo_conditions_end = []
        for i in range(1, 9):
            silo_conditions_end.append(
                (data[f"ProductionLineDisposition_{i}"] == 1)  # Silo 1
                | (data[f"ProductionLineDisposition_{i}"] == 2)  # Silo 2
                | (data[f"ProductionLineDisposition_{i}"] == 3)  # Silo 3
                | (data[f"ProductionLineDisposition_{i}"] == 4)  # Silo 4
                | (data[f"ProductionLineDisposition_{i}"] == 5)  # Silo 5
            )

        data = data.assign(
            event2a_end=(
                (silo_conditions_end[0])  # Position 1
                | (silo_conditions_end[1])  # Position 2
                | (silo_conditions_end[2])  # Position 3
                | (silo_conditions_end[3])  # Position 4
                | (silo_conditions_end[4])  # Position 5
                | (silo_conditions_end[5])  # Position 6
                | (silo_conditions_end[6])  # Position 7
                | (silo_conditions_end[7])  # Position 8
            )
        )

        data = data.assign(
            event2a_end=(
                (data["event2a_end"].shift(1) != True)
                & (
                    (silo_conditions_end[0])  # Position 1
                    | (silo_conditions_end[1])  # Position 2
                    | (silo_conditions_end[2])  # Position 3
                    | (silo_conditions_end[3])  # Position 4
                    | (silo_conditions_end[4])  # Position 5
                    | (silo_conditions_end[5])  # Position 6
                    | (silo_conditions_end[6])  # Position 7
                    | (silo_conditions_end[7])  # Position 8
                )
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self._create_day_data(data)

        # identifying events of 3a (also 3d) type
        day_data = day_data.assign(
            event3a_end=(
                (
                    (day_data["MSDP"] >= day_data["SCHR"])
                    & (day_data["SCHR"] > day_data["TotalFeed"])
                )
                & (
                    (day_data["TotalFeed"] > 0)
                    & (day_data["TotalFeed"] != day_data["SCHR"])
                    & (day_data["TotalFeed"] != day_data["MSDP"])
                    & (day_data["is_noon"])
                )
            )
        )

        day_data = day_data.assign(
            event3a_start=(day_data["event3a_end"].shift(-1) == True)
        )

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self._create_day_data(data)

        # identifying events of 3b type
        day_data = day_data.assign(
            event3b_end=(
                (
                    (
                        (day_data["MSDP"] > day_data["SCHR"])
                        & (day_data["SCHR"] > day_data["TotalFeed"])
                    )
                    | (
                        (day_data["MSDP"] > day_data["TotalFeed"])
                        & (day_data["TotalFeed"] > day_data["SCHR"])
                    )
                )
                & (
                    (day_data["TotalFeed"] > 0)
                    & (day_data["TotalFeed"] != day_data["SCHR"])
                    & (day_data["TotalFeed"] != day_data["MSDP"])
                    & (day_data["is_noon"])
                )
            )
        )

        day_data = day_data.assign(
            event3b_start=(day_data["event3b_end"].shift(-1) == True)
        )

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self._create_day_data(data)

        # identifying events of 3c type
        day_data = day_data.assign(
            event3c_end=(
                (day_data["TotalFeed"] > day_data["MSDP"])
                & (
                    (day_data["TotalFeed"] > 0)
                    & (day_data["TotalFeed"] != day_data["SCHR"])
                    & (day_data["TotalFeed"] != day_data["MSDP"])
                    & (day_data["is_noon"])
                )
            )
        )

        day_data = day_data.assign(
            event3c_start=(day_data["event3c_end"].shift(-1) == True)
        )

        return day_data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def _create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        prod_hourly_data = data.copy()

        # fill information to avoid wrong calculation
        prod_hourly_data.fillna(
            value={
                "ProductionLineStatus": "-",
                "ProductionLineDisposition_1": "-",
                "ProductionLineDisposition_2": "-",
                "ProductionLineDisposition_3": "-",
                "ProductionLineDisposition_4": "-",
                "ProductionLineDisposition_5": "-",
                "ProductionLineDisposition_6": "-",
                "ProductionLineDisposition_7": "-",
                "ProductionLineDisposition_8": "-",
                "TotalFeed": 0,
            },
            inplace=True,
        )

        prod_hourly_data['is_noon'] = (
                (prod_hourly_data['index'].dt.hour == 12) &
                (prod_hourly_data['index'].dt.minute == 0) &
                (prod_hourly_data['index'].dt.second == 0) &
                (prod_hourly_data['index'].dt.microsecond == 0)
            )

        if prod_hourly_data["is_noon"].any():
          prod_day_data = prod_hourly_data[prod_hourly_data['is_noon']]
        else:
          prod_day_data = prod_hourly_data.copy()

        del prod_hourly_data

        # find the year that is in accordance with the indexed date
        prod_day_data["Year"] = prod_day_data["index"].dt.year

        # find the month that is in accordance with the indexed date
        prod_day_data["Month"] = prod_day_data["index"].dt.month

        prod_day_data['MSDP'] = prod_day_data.apply(self._get_msdp_value,
                                msdp_data= self._msdp,
                                data_aux = 'msdp',
                                axis=1)

        prod_day_data['SCHR'] = prod_day_data.apply(self._get_msdp_value,
                                msdp_data= self._msdp,
                                data_aux = 'scheduledRate',
                                axis=1)
        return prod_day_data

    def _get_msdp_value(self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str) -> float:
        """
        retrieves the MSDP from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        # extract filter parameter
        start_time_year = row["Year"]
        start_time_month = row["Month"]

        # create reference date to find MSDP
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (
                msdp_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (msdp_data["year"] == start_time_year)
            & (msdp_data["month"] == start_time_month)
        )
        aux = msdp_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (
                msdp_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = msdp_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (pd.to_datetime(aux["effective_date_datetime"]) - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux[data_aux].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MSDP
        return aux[data_aux].head(1).values[0]

    def calculate_RLT_time_duration(self,
                                    row: pd.DataFrame) -> pd.DataFrame:

        new_value = row['total_duration_seconds']

        condition_3b_rules_1 = ((row['event_definition'] == '3b') & (row['MSDP'] > row['SCHR'])      & (row['SCHR'] > row['TotalFeed']))
        condition_3b_rules_2 = ((row['event_definition'] == '3b') & (row['MSDP'] > row['TotalFeed']) & (row['TotalFeed'] > row['SCHR']))

        if (row['event_definition'] == '3a'):
            new_value = ((row['SCHR'] - row['TotalFeed'])/(row['SCHR']/24)) * 3600

        elif condition_3b_rules_1:
            new_value = ((row['MSDP'] - row['SCHR'])/(row['MSDP']/24)) * 3600

        elif (row['event_definition'] == '3c') or condition_3b_rules_2:
            new_value = ((row['MSDP'] - row['TotalFeed'])/(row['MSDP']/24)) * 3600

        return new_value

    def implement_RLT(self,
                      data: pd.DataFrame,
                      data_cdf: pd.DataFrame
                     ) -> pd.DataFrame:
        """
        calculates the total duration of the eventIII and eventIV (Rate Loss Time calculated in hours, but converted for seconds)

        :param data: row dataframe with the start and the end times of each events
        :type data: pd.DataFrame
        :return: dataframe containing a column with the total duration of each event
        :rtype: pd.DataFrame
        """

        day_data = self._create_day_data(data_cdf)

        all_columns = list(data.columns)
        all_columns.remove('TotalFeed')

        data = pd.merge(
            left=data[all_columns],
            right=day_data[['index', 'MSDP', 'SCHR', 'TotalFeed']],
            how="left",
            left_on="start_time",
            right_on="index",
        )

        data['total_duration_seconds'] = data.apply(self.calculate_RLT_time_duration, axis=1)

        return data.drop(columns = ['index', 'MSDP', 'SCHR'])

    def get_inactive_value(self) -> Optional[str]:
        return None
