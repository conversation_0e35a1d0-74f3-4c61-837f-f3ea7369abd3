from typing import Any, Optional

import numpy as np
import pandas as pd
from app.enums.inactive_value_enum import InactiveValueEnum
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class GELBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
            "4c": self.identify_events_typeIVc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        #  Start Event: U_2_RX_UP = " " or "UP_RX_WASH:1"
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"].isin([" ", "UP_RX_WASH:1"]))
                & (~data["ProductionLineStatus"].shift(1).isin([" ", "UP_RX_WASH:1"]))
            )
        )


        #   End Event: U_2_RX_UP != " " or "UP_RX_WASH:1"        
        data = data.assign(
            event1a_end=(
                (~data["ProductionLineStatus"].isin([" ", "UP_RX_WASH:1"]))
                & (data["ProductionLineStatus"].shift(1).isin([" ", "UP_RX_WASH:1"]))
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pl.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pl.DataFrame
        """

        #  Start Event: U_2_RX_UP = "UP2_RX_LD_VAM:1" or "UP2_RX_LD_WPH:2"
        data = data.assign(
            event3b_start=(
                data["ProductionLineStatus"].isin(["UP2_RX_LD_VAM:1", "UP2_RX_LD_WPH:2"])
                & (~data["ProductionLineStatus"].shift(1).isin(["UP2_RX_LD_VAM:1", "UP2_RX_LD_WPH:2"]))
            )
        )

        #   End Event: U_2_RX_UP =  when value changes from "UP2_RX_TO_PRT:1" to " " or another procedure       
        data = data.assign(
            event3b_end=(
                (data["ProductionLineStatus"].shift(1) == "UP2_RX_TO_PRT:1")
                & (~data["ProductionLineStatus"].isin(["UP2_RX_TO_PRT:1"]))
            )
        )

        return data


    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVa event
        :rtype: pd.DataFrame
        """

        if "ProductionRework" not in data or data["ProductionRework"].empty:
            data["event4a_start"] = False
            data["event4a_end"] = False
            return data

        data = self._create_day_data(data)
        
        data = data.assign(
            event4a_start=(
                (data["ProductionRework"] > 0)
            )
        )

        data = data.assign(
            event4a_end=(
                (data["ProductionRework"] > 0)
            )
        )
        
        data["Date"] = pd.to_datetime(data["Date"])  
        data = data.assign(index=data["Date"].dt.normalize().dt.tz_localize('Europe/Brussels') + pd.to_timedelta(7, unit='h'))

        return data

    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVb event
        :rtype: pd.DataFrame
        """
        if "ProductionScrap" not in data or data["ProductionScrap"].empty:
            data["event4b_start"] = False
            data["event4b_end"] = False
            return data

        data = self._create_day_data(data)
        
        data = data.assign(
            event4b_start=(
                (data["ProductionScrap"] > 0)
            )
        )

        data = data.assign(
            event4b_end=(
                (data["ProductionScrap"] > 0)
            )
        )
        
        data["Date"] = pd.to_datetime(data["Date"])  
        data = data.assign(index=data["Date"].dt.normalize().dt.tz_localize('Europe/Brussels') + pd.to_timedelta(7, unit='h'))
        return data
    
    def identify_events_typeIVc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVb event
        :rtype: pd.DataFrame
        """
        if "ProductionWaste" not in data or data["ProductionWaste"].empty:
            data["event4c_start"] = False
            data["event4c_end"] = False
            return data

        data = self._create_day_data(data)
        
        data = data.assign(
            event4c_start=(
                (data["ProductionWaste"] > 0)
            )
        )

        data = data.assign(
            event4c_end=(
                (data["ProductionWaste"] > 0)
            )
        )
        
        data["Date"] = pd.to_datetime(data["Date"])  
        data = data.assign(index=data["Date"].dt.normalize().dt.tz_localize('Europe/Brussels') + pd.to_timedelta(7, unit='h'))
        
        return data
    
    
    def _create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        data["Date"] = data["index"].dt.date
        
        daily_data = data.groupby("Date").agg({
            "ProductionScrap": "sum",
            "ProductionRework": "sum",
            "ProductionWaste": "sum",
            "ProductionLineStatus": "first",  
            "Product": "first",               
            "ProductDescription": "first",
            "ProcessOrder": "first",
            "BatchID": "first",
            "dt": "sum" 
        }).reset_index()
        
        # daily_data.rename(columns={
        #     "ProductionScrap": "TotalProductionScrap_24h",
        #     "ProductionRework": "TotalProductionRework_24h",
        #     "ProductionWaste": "TotalProductionWaste_24h"
        # }, inplace=True)
        
        return daily_data

    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> float:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        # extract filter parameter
        prod_id = row["Product"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (bbct_data["productId"] == prod_id)
            & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (bbct_data["productId"] == prod_id) & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux["bbct"].head(1).values[0]
    

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            self.get_bbct_value,
            bbct_data=bbct_data,
            axis=1,
        )

        # fix events 3b duration
        data["total_duration_seconds"] -= data["bbct"]

        data = data.query("total_duration_seconds != 0")

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # events which products does not have bbct is reported with duration zero
        data.loc[
            ((data["event_definition"] == "3b") & (data["bbct"] == 0)),
            "total_duration_seconds",
        ] = 0

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data


    def get_best_batch_cycle_time_mt(
        self, row: pd.Series, bbct_data: pd.DataFrame
    ) -> float:
        """
        Retrieves the Best Batch Cycle Time MT (bestBatchCycleTimeMT) from the data stored in the contract.

        :param row: Row containing the product ID and start_time.
        :type row: pd.Series
        :param bbct_data: DataFrame containing BBCT and related data.
        :type bbct_data: pd.DataFrame
        :return: Best Batch Cycle Time MT value.
        :rtype: float
        """
        # Extract filter parameters
        prod_id = row["Product"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # Create reference date to find the bestBatchCycleTimeMT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # First filter: productId, reportingLineExternalId, year, and month
        filter_1 = (
            (bbct_data["productId"] == prod_id)
            & (bbct_data["reportingLineExternalId"] == self._reporting_line_external_id)
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # Second filter: productId and reportingLineExternalId (if the first one fails)
        if aux.shape[0] == 0:
            filter_2 = (
                (bbct_data["productId"] == prod_id)
                & (bbct_data["reportingLineExternalId"] == self._reporting_line_external_id)
            )
            aux = bbct_data.loc[filter_2, :]

            # If still no data, return 0
            if aux.shape[0] == 0:
                return 0

            # Order by the most recent year considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # Fill values to get the most recent date preceding the event start date
            aux.fillna(method="ffill", inplace=True)
            aux["bestBatchCycleTimeMT"].fillna(0, inplace=True)

            # Ensure ordering by the most recent year considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # Return the Best Batch Cycle Time MT
        return aux["bestBatchCycleTimeMT"].head(1).values[0]


    def fix_events_type4_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Volume from every event of type 4

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        
        # Check if there are events of type 4a, 4b, or 4c
        if not data["event_definition"].isin(["4a", "4b", "4c"]).any():
            return data  # Return unaltered DataFrame if no such events exist

      
        data["bbct_mt"] = data.apply(
            self.get_best_batch_cycle_time_mt,
            bbct_data=bbct_data,
            axis=1,
        )
        
        # apply extraction of the BBCT value
        data["bbct"] = data.apply(
            self.get_bbct_value,
            bbct_data=bbct_data,
            axis=1,
        )

        # Create 'qtd' column using vectorized logic
        data["qtd"] = np.select(
            [
                data["event_definition"] == "4a",
                data["event_definition"] == "4b",
                data["event_definition"] == "4c",
            ],
            [
                data["ProductionRework"],
                data["ProductionScrap"],
                data["ProductionWaste"],
            ],
            default=0,
        )

        # Fix events type 4 - duration
        valid_rows = (
            (data["event_definition"].isin(["4a", "4b", "4c"]))  # Valid event types
            & (data["bbct_mt"] != 0)  # Avoid division by zero
        )
        data.loc[valid_rows, "total_duration_seconds"] = (
            data.loc[valid_rows, "bbct"] * data.loc[valid_rows, "qtd"] / data.loc[valid_rows, "bbct_mt"]
        )

        # Fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)
        
        # Ensure total_duration_seconds is zero when bbct is zero for events type 4
        bbct_zero_rows = (
            (data["event_definition"].isin(["4a", "4b", "4c"]))  # Valid event types
            & (data["bbct"] == 0)
        )
        data.loc[bbct_zero_rows, "total_duration_seconds"] = 0


        # Drop created columns
        data.drop(columns=["bbct_mt", "qtd"], inplace=True)

        return data

    def get_inactive_value(self) -> Optional[str]:
        return InactiveValueEnum.INACTIVE.value
