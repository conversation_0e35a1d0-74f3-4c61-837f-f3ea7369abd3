from datetime import time
from typing import Any, List, Optional, Union

import numpy as np
import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.base_service import BaseService
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class SpsEstEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

         # event trigger start - ProductionLineStatus1 == 0
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus1"] == 0) & (
                 data["ProductionLineStatus1"].shift(1) != 0  
                )
            )
        )

        # event end - ProductionLineStatus1 > 0
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus1"] > 0) & (
                 data["ProductionLineStatus1"].shift(1) <= 0
                )
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus2 > 0 and ProductionLineStatus1 > 0
        event2a_start = (data["ProductionLineStatus2"] > 0) & (
            data["ProductionLineStatus1"] > 0) & (
            data["ProductionLineStatus3"] <= 1   
            )

        # event end - ProductionLineStatus3 > 1
        event2a_end = (data["ProductionLineStatus3"] > 1) & (
            data["ProductionLineStatus3"].shift(1) <= 1
        )

        # correct start and end flags
        data = data.assign(
            event2a_start=event2a_start, event2a_end=event2a_end
        )
        event2a_start = (data["event2a_start"] == True) & (
            data["event2a_start"].shift(1) != True)
        
        data = data.assign(
            event2a_start=event2a_start
        )

        return data

    def identify_events_typeIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus1  1
        event2b_start = (data["ProductionLineStatus1"] == 0) & (
                data["ProductionLineStatus1"].shift(1) != 0) & (
                data["ProductionLineStatus2"] != 0    
                )
        

        # event end - ProductionLineStatus2 == 0
        event2b_end = (data["ProductionLineStatus2"] == 0) & (
                data["ProductionLineStatus2"].shift(1) != 0)

        # correct start and end flags
        data = data.assign(
            event2b_start=event2b_start, event2b_end=event2b_end
        )

        return data

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3a_end=(
                (data["MSDP"] >= data["SCHR"])
                & (data["SCHR"] > data["NetProduction"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )
        data = data.assign(
            event3a_start=(data["event3a_end"].shift(-1) == True)
        )
        data = data.assign(
            total_duration_seconds=(
                (data["SCHR"] - data["NetProduction"])
                / (data["SCHR"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3b_end=(
                (data["MSDP"] >= data["SCHR"])
                & (data["SCHR"] > data["NetProduction"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["SCHR"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3c_end=(
                (data["MSDP"] > data["NetProduction"])
                & (data["NetProduction"] > data["SCHR"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["SCHR"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3c_start=(data["event3c_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["NetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3d_end=(
                (data["NetProduction"] > data["MSDP"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3d_start=(data["event3d_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["NetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_waste_data(data)

        data = data.assign(
            event4a_end=(
                (
                    data["WasteFeed"]
                    - data["WasteFeed"].shift(1)
                    < 0
                )
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event4a_start=(data["event4a_end"].shift(1) == True)
        )

        data = data.assign(
            total_duration_seconds=data["WasteProduced"] * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIVb(self):
        pass

    def __create_day_waste_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support event 4a

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        TONNES_MULTIPLICATION_FACTOR = 0.00099

        data["WasteFeed"] = (
            data["WasteFeed"] * TONNES_MULTIPLICATION_FACTOR
        )

        data["WasteMass"] = (data["WasteFeed"] / 3600) * data["dt"]
        data.set_index("index", inplace=True)
        data["TotalWaste"] = data["WasteMass"].rolling("24h").sum()
        data.reset_index(inplace=True)

        data["is_midnight"] = (
            (data["index"].dt.hour == 0)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        # find the year that is in accordance with the indexed date
        data["Year"] = data["index"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_data["msdp"] = msdp_data.apply(
            lambda x: x["scheduledRate"]
            if x["scheduledRate"] > 0
            else x["msdp"],
            axis=1,
        )

        data["MSDP"] = data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )

        data["WasteProduced"] = (data["TotalWaste"]) / (data["MSDP"] / 24)

        return data

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """


        data["is_midnight"] = (
            (data["index"].dt.hour == 00)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
          data = data[data["is_midnight"]]


        # find the year that is in accordance with the indexed date
        data["Year"] = data["index"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
        self._msdp["reportingLineExternalId"]
        == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand",
        )

        # Defining NetProduction based on productGroup
        def get_net_production(row):
            if pd.notna(row["Product"]):
                if row["Product"] == "EtAc":
                    return row["NetProduction1"]
                elif row["Product"] == "n-BuAc":
                    return row["NetProduction2"]
            return 0

        # Apply the function line by line
        data["NetProduction"] = data.apply(get_net_production, axis=1)

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: Union[str, List[str]]
    ) -> Union[float, pd.Series]:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float or pd.Series
        """
        # SPS has special product handling - product name needs to be contained in product group
        product = row.get("ProductDescription")
        if pd.isna(product):
            return 0 if isinstance(data_aux, str) else pd.Series([0] * len(data_aux))

        # Create custom product filter - check if product is contained in any product group
        products_df = msdp_data[
            msdp_data["productGroup"].apply(lambda x: x in product)
        ]["productGroup"].drop_duplicates()
        
        relative_product_name = "" if len(products_df) == 0 else str(products_df.iloc[0])
        product_filter = msdp_data["productGroup"] == relative_product_name
        
        return msdp_util_get_msdp_value(row, msdp_data, data_aux, product_filter)

    def get_inactive_value(self) -> Optional[str]:
        return None
