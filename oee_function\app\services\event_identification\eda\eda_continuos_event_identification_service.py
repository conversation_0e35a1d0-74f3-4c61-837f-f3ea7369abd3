from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class EdaEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Event trigger start - ProductionLineStatus goes from > 500 to 300-500 
        event1a_start = (
            (data["ProductionLineStatus"].shift(1) > 700)
            & (data["ProductionLineStatus"] <= 700)
            & (data["ProductionLineStatus"] >= 100)
        )

        # Event trigger end - ProductionLineStatus is outside 300-500 range
        event1a_end = (
            (
                ~data["ProductionLineStatus"].between(300, 500)
            )  # Current status is outside 300-500 range
            & (
                data["ProductionLineStatus"].shift(1).between(300, 500)
            )  # Previous status was inside 300-500 range
        )

        

        data = data.assign(
            event1a_start=event1a_start, event1a_end=event1a_end
        )
        new_df = data[
            (data["event1a_start"] == True) | (data["event1a_end"] == True)
        ]
        start_times = []
        end_times = []
        time_diffs = []

        # get the valid events1a that lasts more than 3 minutes
        for i in range(1, len(new_df)):
            if (
                new_df["event1a_start"].iloc[i - 1] == True
                and new_df["event1a_start"].iloc[i] == False
            ):
                start_time = new_df["index"].iloc[i - 1]
                end_time = new_df["index"].iloc[i]
                time_diff = end_time - start_time
                if (
                    time_diff.total_seconds() > 180
                ):  
                    start_times.append(start_time)
                    end_times.append(end_time)
                    time_diffs.append(time_diff)

        result = pd.DataFrame(
            {
                "start_time": start_times,
                "end_time": end_times,
                "time_diff": time_diffs,
            }
        )
        filtered_data = data[data['index'].isin(result['start_time']) | data['index'].isin(result['end_time'])]

        return filtered_data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events of type Ic.


        """
        # Event trigger end - ProductionLineStatatus == 0
        event1c_start = (data["ProductionLineStatus"] == 0) & (
            data["ProductionLineStatus"].shift(1) != 0
        )

        # Event trigger end - ProductionLineStatus changes to 10
        event1c_end = (data["ProductionLineStatus"] >= 10) & (
            data["ProductionLineStatus"].shift(1) < 10
        )

        data = data.assign(
            event1c_start=event1c_start, event1c_end=event1c_end
        )

        return data

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events type IIa

        """
        # ProductionLineStatus goes from 0 to >= 10 and lower than 500
        event2a_start = (
            (data["ProductionLineStatus"] >= 10)
            & (data["ProductionLineStatus"] < 500)
        ) & (data["ProductionLineStatus"].shift(1) < 10)

        # ProductionLineStatus > 500
        event2a_end = (data["ProductionLineStatus"] > 500) & (
            data["ProductionLineStatus"].shift(1) <= 500
        )
        data = data.assign(
            event2a_start=event2a_start, event2a_end=event2a_end
        )
        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_inactive_value(self) -> Optional[str]:
        return None
