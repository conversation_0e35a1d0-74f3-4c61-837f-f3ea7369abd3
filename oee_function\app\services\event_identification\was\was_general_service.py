import pandas as pd

from .was_fill_event_identification_service import WASFILSettings

class WashingtonGeneralService:
    """
    Service for general operations related to Washington event identification.
    """

    def __init__(self, reporting_line_external_id: str):
        self.reporting_line_external_id = reporting_line_external_id
        self.was_fil_settings = WASFILSettings()

    def prepare_hourly_data(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Prepares hourly data for the given reporting line.
        
        :param data: DataFrame containing the time series data.
        :param reporting_line_external_id: External ID of the reporting line.
        :return: DataFrame with hourly data prepared.
        """
        
        if self.reporting_line_external_id.startswith("RLN-WASFIL"):  # WASHINGTON FILLAMENTS
            data = self.was_fil_settings.prepare_hourly_data(data)
            data = self.was_fil_settings.create_total_feed_column(data)
        
        return data
