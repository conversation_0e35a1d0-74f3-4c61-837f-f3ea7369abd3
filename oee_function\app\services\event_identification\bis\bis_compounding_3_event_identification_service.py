from typing import Any

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.hourly_data_compounding import (
    HourlyDataCompounding,
)


class BisCompoundingThirdEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        mdr: pd.DataFrame,
    ) -> None:
        super().__init__()
        if mdr is not None:
            self._valid_prod_ids = list(mdr.productId.unique())
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "4a": self.identify_events_typeIVa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus != 'RUNNING' (0) and Product = Product ID that is ON list
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"] != "Running")
                & (data["ProductionLineStatus"].shift(1) == "Running")
                & (data["Product"].isin(self._valid_prod_ids))
            )
        )

        # event trigger end   - ProductionLineStatus == 'RUNNING' (0) or Product = Product ID that is NOT ON list
        data = data.assign(
            event1a_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                )
                | (
                    (~data["Product"].isin(self._valid_prod_ids))
                    & (data["Product"].shift(1).isin(self._valid_prod_ids))
                )
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIa event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus = 'RUNNING' (0) and Product = Product ID that is NOT ON list

        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] == "Running")
                & (~data["Product"].isin(self._valid_prod_ids))
            )
        )

        # event trigger end   - ProductionLineStatus != 'RUNNING' (0) or Product = Product ID that is ON list

        data = data.assign(
            event2a_end=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    | (data["Product"].isin(self._valid_prod_ids))
                )
                & (data["event2a_start"].shift(1) == True)
            )
        )

        # fix event trigger start

        data = data.assign(
            event2a_start=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (~data["Product"].isin(self._valid_prod_ids))
                )
                & (data["event2a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IIc event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus != 'RUNNING' (0) and Product = Product ID that is NOT ON list

        data = data.assign(
            event2c_start=(
                (data["ProductionLineStatus"] != "Running")
                & (~data["Product"].isin(self._valid_prod_ids))
            )
        )

        # event trigger end - ProductionLineStatus = 'RUNNING' (0) and (Product = Product ID of start event) or Product = Product that is ON list

        data = data.assign(
            event2c_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2c_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                    & (data["Product"].isin(self._valid_prod_ids))
                )
                | (
                    (data["Product"].isin(self._valid_prod_ids))
                    & (data["Product"] != data["Product"].shift(1))
                )
            )
        )

        # fix event trigger start

        data = data.assign(
            event2c_start=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    & (~data["Product"].isin(self._valid_prod_ids))
                )
                & (data["event2c_start"].shift(1) != True)
            )
        )

        # filter only true booleans to get the correct events

        data = data[
            (data["event2c_start"] == True) | (data["event2c_end"] == True)
        ]

        data = data.assign(
            event2c_start=(
                (data["event2c_start"] == True)
                & (data["Product"] == data["Product"].shift(-1))
            )
        )

        data = data.assign(
            event2c_end=(
                (data["event2c_end"] == True)
                & (data["Product"] == data["Product"].shift(1))
            )
        )

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IId

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IId event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus != 'RUNNING' (0) and Product = Product ID that is NOT ON list

        data = data.assign(
            event2d_start=(
                (data["ProductionLineStatus"] != "Running")
                & (~data["Product"].isin(self._valid_prod_ids))
            )
        )

        # event trigger end - ProductionLineStatus = 'RUNNING' (0) and (Product = Product ID of start event) or Product = Product that is ON list

        data = data.assign(
            event2d_end=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["event2d_start"].shift(1) == True)
                )
                | (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductionLineStatus"].shift(1) != "Running")
                    & (data["Product"].isin(self._valid_prod_ids))
                )
                | (
                    (data["Product"].isin(self._valid_prod_ids))
                    & (data["Product"] != data["Product"].shift(1))
                )
            )
        )

        # fix event trigger start

        data = data.assign(
            event2d_start=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    & (~data["Product"].isin(self._valid_prod_ids))
                )
                & (data["event2d_start"].shift(1) != True)
            )
        )

        # filter only true booleans to get the correct events

        data = data[
            (data["event2d_start"] == True) | (data["event2d_end"] == True)
        ]

        data = data.assign(
            event2d_start=(
                (data["event2d_start"] == True)
                & (data["Product"] != data["Product"].shift(-1))
            )
        )

        data = data.assign(
            event2d_end=(
                (data["event2d_end"] == True)
                & (data["Product"] != data["Product"].shift(1))
            )
        )

        return data

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IVa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type IVa event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus = 'RUNNING' (0) and ProductValve = 'RERUN'(0)

        data = data.assign(
            event4a_start=(
                (data["ProductionLineStatus"] == "Running")
                & (data["ProductValve"] == 0)
            )
        )

        # event trigger end - ProductionLineStatus != 'RUNNING' (0) or ProductValve = 'PRODUCT'(1) or PelletizerRunning = 'down' (< 100)
        # or PDV valve = 'FLOOR'

        data = data.assign(
            event4a_end=(
                (
                    (data["ProductionLineStatus"] != "Running")
                    & (data["ProductionLineStatus"].shift(1) == "Running")
                )
                | (
                    (data["ProductValve"] == 1)
                    & (data["ProductValve"].shift(1) != 1)
                )
            )
        )

        # fix event trigger start

        data = data.assign(
            event4a_start=(
                (
                    (data["ProductionLineStatus"] == "Running")
                    & (data["ProductValve"] == "Running")
                )
                & (data["event4a_start"].shift(1) != True)
            )
        )

        return data
