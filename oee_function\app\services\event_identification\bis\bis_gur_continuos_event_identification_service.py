from datetime import time
from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class BisGurContinuosEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
            "4c": self.identify_events_typeIVc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def __fix_missing_production_status_line(
        self, data: pd.DataFrame
    ) -> pd.DataFrame:
        keys = [
            "ProductionLineStatus1",
            "ProductionLineStatus2",
            "ProductionLineStatus3",
            "ProductionLineStatus4",
            "ProductionLineStatus5",
            "ProductionLineStatus6",
        ]
        return data.assign(**{key: data[key].fillna(0) for key in keys})

    # product_list = list(BisGurProductMappingService()._mapping_dict.values())
    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        start_key = "event1a_start"
        end_key = "event1a_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (~data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))

        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        # Correct start flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )

        # Correct end flags
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """
        data = self.__fix_missing_production_status_line(data)

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # and Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        start_key = "event2a_start"
        end_key = "event2a_end"
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) & (data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # OR Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) | (data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # correct start and end flags

        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIc

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)
        start_key = "event2c_start"
        end_key = "event2c_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 < 100
        # AND Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (~data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        # Product is equal to the product at the start of the event
        data[start_key] = data[start_key] & (
            data["Product"] == data["Product"].shift(-1)
        )
        data[end_key] = data[end_key] & (
            data["Product"] == data["Product"].shift(1)
        )

        return data

    def identify_events_typeIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)
        start_key = "event2d_start"
        end_key = "event2d_end"

        # event trigger start - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is in ['Trial 6', 'Trial 12', 'Trial Max']
        data[start_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))

        # event trigger end - ProductionLineStatus1  + ProductionLineStatus2 + ProductionLineStatus3 >= 100
        # AND Product is not in ['Trial 6', 'Trial 12', 'Trial Max']
        data[end_key] = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            >= 100
        ) | (~data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))
        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))
        # Product diffs to the product at the start of the event
        data[start_key] = data[start_key] & (
            data["Product"] != data["Product"].shift(-1)
        )
        data[end_key] = (data[end_key]) & (
            data["Product"] != data["Product"].shift(1)
        )

        return data

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)
        start_key = "event3a_start"
        end_key = "event3a_end"
        #  if ProductionLineStatus6 == 4 it corresponds to Run
        # if ProductionLineStatus6 == 5 it corresponds to Stop

        data["lineSum"] = (
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        )

        data["dt"] = data["dt"] / 60

        data["sum_less_than_100_duration"] = data.groupby(
            (
                (data["lineSum"].shift(1).fillna(0) >= 100)
                & (data["lineSum"] < 100)
            ).cumsum()
        )["dt"].cumsum()
        data.loc[data["lineSum"] >= 100, "sum_less_than_100_duration"] = 0

        data[start_key] = (
            (data["sum_less_than_100_duration"].shift(1) >= 15)
            & (data["lineSum"] >= 100)
        )

        data[end_key] = (
            (
                (data["lineSum"].shift(1) >= 100)
                & (data["lineSum"] < 100)
            )
            | (
                (data["ProductionLineStatus4"] > 10)
                & (data["ProductionLineStatus5"] > 1)
                & (data["ProductionLineStatus6"] == 4)
            )
        )
        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__fix_missing_production_status_line(data)

        start_key = "event3b_start"
        end_key = "event3b_end"
        #  if ProductionLineStatus6 == 4 it corresponds to Run
        # if ProductionLineStatus6 == 5 it corresponds to Stop

        data["lineSum"] = (
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        )

        data[start_key] = (
            (data["ProductionLineStatus5"] < 1)
            & (data["ProductionLineStatus6"] == 5)
        )

        data[end_key] = (
            data["lineSum"] < 100
        )

        # correct start and end flags
        data[start_key] = data[start_key] & (
            ~data[start_key].shift(1).fillna(False)
        )
        data[end_key] = data[end_key] & (~data[end_key].shift(1).fillna(False))

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIVa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IVa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        day_data = self.__create_day_data(data)
        
        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIVb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IVb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        day_data = self.__create_day_data(data)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[self._rlt_key] < 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIVc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IVc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        day_data = self.__create_day_data(data)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[self._rlt_no_demand_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 4a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        data = self.__fix_missing_production_status_line(data)

        total_produced_key = "total_produced"

        data[total_produced_key] = (
            (
                data["ProductionLineStatus1"]
                * (data["ProductionLineStatus1"] > 10)
            )
            + (
                data["ProductionLineStatus2"]
                * (data["ProductionLineStatus2"] > 10)
            )
            + (
                data["ProductionLineStatus3"]
                * (data["ProductionLineStatus3"] > 10)
            )
        )
        per_hour_to_per_sec_factor = 1 / 3600
        kg_to_ton_factor = 1 / 1000
        total_bellow_100_key = "total_bellow_100"
        total_greater_equals_100_key = "total_greater_equals_100"

        data[total_produced_key] = (
            data[total_produced_key]
            * kg_to_ton_factor
            * per_hour_to_per_sec_factor
            * data["dt"]
        )
        data[total_bellow_100_key] = (
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        ) < 100

        data[total_greater_equals_100_key] = (
            data["ProductionLineStatus1"]
            + data["ProductionLineStatus2"]
            + data["ProductionLineStatus3"]
        ) >= 100

        running_time_key = "running_time"
        
        # Modified running_time calculation
        not_running_condition = (
            (
                data["ProductionLineStatus1"]
                + data["ProductionLineStatus2"]
                + data["ProductionLineStatus3"]
            )
            < 100
        ) & (~data["Product"].isin(["Trial 6", "Trial 12", "Trial Max"]))
        
        data["condition_running_time"] = not_running_condition
        
        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )
        
        #Factor to transform MLB to TONS
        MLB_TO_TONS = 0.453592
        
        data[total_produced_key] = (
            data[total_produced_key] * MLB_TO_TONS
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "Product"], dropna=False
        ).agg(
            {
                total_bellow_100_key: "all",
                total_greater_equals_100_key: "all",
                total_produced_key: "sum",
                "duration_in_seconds": "sum",
                "ScheduledRate": "first",
            }
        )
        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})

        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data = data.loc[data.groupby("timestamp")["total_produced"].idxmax()]
        
        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        data_to_get_products_from = data.copy().drop(
            labels=[
                col
                for col in data.columns
                if col not in ["timestamp", "Product"]
            ],
            axis=1,
        )

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"

        per_day_to_per_hour_factor = 1 / 24

        if data.empty:
            return data

        
        data[[msdp_key, "ScheduledRate"]] = (
            data.apply(
                self.get_msdp_value,
                msdp_data=msdp_data,
                data_aux=[msdp_key, "scheduledRate"],
                axis=1,
                result_type="expand",
            )
            * per_day_to_per_hour_factor
        )

        data["ScheduledRate"] = data["ScheduledRate"] * per_day_to_per_hour_factor

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001

        rlt_schema = [
            (
                # 4a
                "4a",
                lambda df: (df[total_bellow_100_key]),
                # total bellow 100
                {rlt_key: lambda _: 0},
            ),
            (
                "4b",
                lambda df: (df[total_produced_key] - df[msdp_key]).abs()
                < tolerance,  # produced = msdp
                {rlt_key: lambda _: 0},
            ),
            (
                "4c1",
                lambda df: (
                    (df[total_produced_key] - df[msdp_key]) > tolerance
                )
                & (
                    df[total_greater_equals_100_key]
                ),  # produced > msdp & at total > 100
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key]
                },
            ),
            (
                "4c2",
                lambda df: (
                    (df[total_produced_key] - df[msdp_key]) > tolerance
                )
                & (
                    df[total_greater_equals_100_key] == False
                ),  # produced > msdp & not total >= 100 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[msdp_key]
                },
            ),
            (
                "4d1",
                lambda df: ((df[msdp_key] - df["ScheduledRate"]) > tolerance)
                & ((df["ScheduledRate"] - df[total_produced_key]) > tolerance)
                & (
                    df[total_greater_equals_100_key]
                ),  # msdp > scheduled & scheduled > produced & total >= 100 during hour
                {
                    rlt_key: lambda df: (
                        df["ScheduledRate"]
                        - df[total_produced_key]
                    )
                    / df["ScheduledRate"],
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df["ScheduledRate"]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4d2",
                lambda df: ((df[msdp_key] - df["ScheduledRate"]) > tolerance)
                & ((df["ScheduledRate"] - df[total_produced_key]) > tolerance)
                & (
                    df[total_greater_equals_100_key] == False
                ),  # msdp > scheduled & scheduled > produced & total >= 100
                {
                    rlt_key: lambda df: (
                        (df["ScheduledRate"] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df["ScheduledRate"],
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - (df["ScheduledRate"] * df[running_time_key])
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4e1",
                lambda df: (
                    (df[msdp_key] - df[total_produced_key]) > tolerance
                )
                & ((df[total_produced_key] - df["ScheduledRate"]) > tolerance)
                & (
                    df[total_greater_equals_100_key]
                ),  # msdp > produced & produced > scheduled & total >= 100
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4e2",
                lambda df: (
                    (df[msdp_key] - df[total_produced_key]) > tolerance
                )
                & ((df[total_produced_key] - df["ScheduledRate"]) > tolerance)
                & (
                    df[total_greater_equals_100_key] == False
                ),  # msdp > produced & produced > scheduled & total >= 100
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - df[total_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "4f1",
                lambda df: (
                    (df[msdp_key] - df["ScheduledRate"]).abs() < tolerance
                )
                & ((df["ScheduledRate"] - df[total_produced_key]) > tolerance)
                & (
                    df[total_greater_equals_100_key]
                ),  # msdp = produced & scheduled > produced & total >= 100
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_produced_key])
                    / df[msdp_key],
                },
            ),
            (
                "4f2",
                lambda df: (
                    (df[msdp_key] - df["ScheduledRate"]).abs() < tolerance
                )
                & ((df["ScheduledRate"] - df[total_produced_key]) > tolerance)
                & (
                    df[total_greater_equals_100_key] == False
                ),  # msdp = produced & scheduled > produced & total >= 100
                {
                    rlt_key: lambda df: ((df[msdp_key] * df[running_time_key]) - df[total_produced_key])
                    / df[msdp_key],
                },
            ),
            (
                "4g",
                lambda df: ((df[msdp_key] - df["ScheduledRate"]) > tolerance)
                & (df["ScheduledRate"] - df[total_produced_key].abs() < tolerance),
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df["ScheduledRate"]
                    ) / df[msdp_key],
                },
            ),
        ]
        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")
        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(
                    data.loc[filter_mask]
                )
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)
        
        # no events should be generated if msdp_key <= 0.1
        data = data[data[msdp_key] > 0.1]

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    total_produced_key: "sum",
                    rlt_key: "sum",
                    "ScheduledRate": "first",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )
        # no events should be generated if total_produced <= 0
        data = data[data[total_produced_key] > 0]
        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )

        # # get msdp on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day
        
        data[[msdp_key, "ScheduledRate"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=[msdp_key, "scheduledRate"],
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_produced_key] - data[msdp_key]).abs() < tolerance)
            & (
                (data[total_produced_key] - data["ScheduledRate"]).abs()
                < tolerance
            )
            & ((data[msdp_key] - data["ScheduledRate"]).abs() < tolerance)
        )
        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["Product"]
        )

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = (
            data[rlt_no_demand_key] * hours_to_seconds_factor
        )

        self._day_data = data

        return data.copy()

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def get_inactive_value(self) -> Optional[str]:
        return None
