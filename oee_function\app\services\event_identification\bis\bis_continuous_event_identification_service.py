from datetime import time
from typing import Any

import numpy as np
import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value
from app.utils.product_matching_utils import create_product_match_filter

from ...tag_value_mapping.obh.obh_product_mapping_service import (
  ObhProductMappingService,
)


class BisContinuousEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "2a": self.identify_events_typeIIa,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus < 5
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus"].shift(1) >= 5)
                & (data["ProductionLineStatus"] < 5)
            )
        )

        # event trigger end - ProductionLineStatus > 5
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus"].shift(1) <= 5)
                & (data["ProductionLineStatus"] > 5)
            )
        )

        return data

        pass

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type II included
        :rtype: pd.DataFrame
        """
        
        # event trigger start - ProductionLineStatus > 5 AND ProductDescription = "RISER"
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus"] > 5)
                & (data["ProductDescription"] == "RISER")
                & (data["ProductDescription"].shift(1) != "RISER")
            )
        )

        # event trigger end - ProductionLineStatus != "RISER"
        data = data.assign(
            event2a_end=(
                (data["ProductDescription"].shift(1) == "RISER")
                & (data["ProductDescription"] != "RISER")
            )
        )

        return data

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args):
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        day_data = self.__create_day_data(data)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data


    def identify_events_typeIIIb(self, data: pd.DataFrame, **args):
        day_data = self.__create_day_data(data)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[self._rlt_key] < 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args):
        day_data = self.__create_day_data(data)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_no_demand_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data


        total_produced_key = "total_produced"

        data[total_produced_key] = data["ProductionLineStatus"]

        MLB_TO_LB_MULTIPLICATION_FACTOR = 1000
        
        per_hour_to_per_sec_factor = 1 / 3600

        data[total_produced_key] = (
            (
                data[total_produced_key]
                * per_hour_to_per_sec_factor
                * data["dt"]
            )
            * 0.69
        ) / 2000

        # Convert Thousand Pounds to Pounds
        data[total_produced_key] = data[total_produced_key] * MLB_TO_LB_MULTIPLICATION_FACTOR

        running_time_key = "running_time"

        # Modified running_time calculation
        not_running_condition = (data["ProductionLineStatus"] < 5)

        data["condition_running_time"] = not_running_condition

        data["duration_in_seconds_without_conditions"] = (data["index"].diff().dt.total_seconds().fillna(0))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                total_produced_key: "sum",
                "duration_in_seconds": "sum",
                "ProductionLineStatus": "mean",
            }
        )
        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})

        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )

        data.drop(columns=["duration_in_seconds"],inplace=True)

        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data = data.loc[data.groupby("timestamp")["total_produced"].idxmax()]

        data_to_get_products_from = data.copy().drop(
            labels=[
                col
                for col in data.columns
                if col
                not in ["timestamp", "ProductDescription", total_produced_key]
            ],
            axis=1,
        )

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        scheduled_rate_key = "scheduledRate"

        per_day_to_per_hour_factor = 1 / 24

        data[[msdp_key, scheduled_rate_key]] = (
            data.apply(
                self.get_msdp_value,
                msdp_data=msdp_data,
                data_aux=[msdp_key, scheduled_rate_key],
                axis=1,
                result_type="expand",
            )
        )

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001

        rlt_schema = [
            (  # 3a
                "3a",
                lambda df: (df["ProductionLineStatus"] < 5),
                {rlt_key: lambda _: 0},
            ),
            (
                "3b",
                lambda df: (df[total_produced_key] - (df[msdp_key] / 24)).abs()
                < tolerance,
                {rlt_key: lambda _: 0},
            ),
            (
                "3c1",
                lambda df: (df[total_produced_key] > (df[msdp_key] / 24))
                & (df[running_time_key] >= 0.99),
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24)
                },
            ),
            (
                "3c2",
                lambda df: (df[total_produced_key] > (df[msdp_key] / 24))
                & (df[running_time_key] < 0.99),
                {
                    rlt_key: lambda df: (
                        (
                            (df[msdp_key] * (df[running_time_key] / 24))
                            - df[total_produced_key]
                        )
                        / (df[msdp_key] / 24)
                    )
                },
            ),
            (
                "3d1",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key]) / 24)
                    > tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (df[running_time_key] >= 0.99),
                {
                    rlt_key: lambda df: (
                        (
                            (df[scheduled_rate_key] / 24)
                            - df[total_produced_key]
                        )
                        / (df[scheduled_rate_key] / 24)
                    ),
                    rlt_no_demand_key: lambda df: (
                        ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24))
                        / (df[msdp_key] / 24)
                    ),
                },
            ),
            (
                "3d2",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24))
                    > tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (df[running_time_key] < 0.99),
                {
                    rlt_key: lambda df: (
                        (
                            (
                                df[scheduled_rate_key]
                                * (df[running_time_key] / 24)
                            )
                            - df[total_produced_key]
                        )
                        / (df[scheduled_rate_key] / 24)
                    ),
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] / 24)
                        - (df[scheduled_rate_key] / 24)                        
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3e1",
                lambda df: (
                    ((df[msdp_key] / 24) - df[total_produced_key]) > tolerance
                )
                & (
                    (df[total_produced_key] - (df[scheduled_rate_key]) / 24)
                    > tolerance
                )
                & (df[running_time_key] >= 0.99),
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3e2",
                lambda df: (
                    ((df[msdp_key] / 24) - df[total_produced_key]) > tolerance
                )
                & (
                    (df[total_produced_key] - (df[scheduled_rate_key] / 24))
                    > tolerance
                )
                & (df[running_time_key] < 0.99),
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] / 24)
                        - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3f1",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    < tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (df[running_time_key] >= 0.99),
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3f2",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    < tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (df[running_time_key] < 0.99),
                {
                    rlt_key: lambda df: (
                        ((df[msdp_key] / 24) * (df[running_time_key] / 24))
                        - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3g",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    > tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    < tolerance
                ),
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)
                    )
                    / (df[msdp_key] / 24),
                },
            ),
        ]

        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")

        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(
                    data.loc[filter_mask]
                )
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)

        # no events should be generated if msdp_key <= 0.1
        data = data[data[msdp_key] > 0.1]

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    scheduled_rate_key: "sum",
                    total_produced_key: "sum",
                    rlt_key: "sum",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )

        # no events should be generated if total_produced <= 0
        data = data[data[total_produced_key] > 0]

        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )

        # get msdp and scheduled rate on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day
        data[[msdp_key, scheduled_rate_key]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=[msdp_key, scheduled_rate_key],
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_produced_key] - data[msdp_key]).abs() < tolerance)
            & (
                (data[total_produced_key] - data[scheduled_rate_key]).abs()
                < tolerance
            )
            & ((data[msdp_key] - data[scheduled_rate_key]).abs() < tolerance)
        )

        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["ProductDescription"]
        )

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = (
            data[rlt_no_demand_key] * hours_to_seconds_factor
        )

        data.replace([np.inf, -np.inf], np.nan, inplace=True)
        data = data.dropna()

        self._day_data = data

        return data.copy()

    def get_msdp_value(
        self,
        row: pd.Series,
        msdp_data: pd.DataFrame,
        data_aux: str | list[str],
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract with product filtering
        """
        product = row["ProductDescription"]
        if pd.isna(product):
            return pd.array([0] * len(data_aux))

        combined_mask = create_product_match_filter(msdp_data, product)
        
        if not combined_mask.any():
            product_filter = msdp_data["productGroup"] == ""
        else:
            product_filter = combined_mask
        
        return utils_get_msdp_value(row, msdp_data, data_aux, product_filter)
