# Introduction
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project.

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references


---

## How it works

This project is divided into three parts:

1. Pipeline (oee_function folder)
2. data deployment parameterized data and pipeline configuration  (scripts folder)

### Pre-requisites

Before you begin, you will need to have the following tools installed on your machine:
[Git](https://git-scm.com), [poetry](https://python-poetry.org/).
In addition, it is good to have an editor to work with the code like [VSCode](https://code.visualstudio.com/)

#### Rodando o Pipeline (oee_function)

```bash


# Access the project folder cmd/terminal
$ cd oee_function

# install pyenv
$ curl https://pyenv.run | bash

# install version python
$ pyenv install 3.11.9


# return the initial state of the pip
$ pip freeze | grep -v "^-e" | xargs pip uninstall -y

# install pipx
$ pip install pipx

# install poetry in pipx
$ pipx install poetry

# tells poetry that it should manage python virtual environments
$ poetry config virtualenvs.in-project true

# informs which python will be used in the project
$ pyenv local 3.11.9

# makes poetry create a virtual environment with the project's python version
$ poetry env use 3.11.9

# activate python virtual environment
$ poetry shell

# install project dependencies
$ poetry install

# shows all project dependencies
$ poetry show

# export the requirements
$ poetry export --without-hashes --format=requirements.txt > requirements.txt

# exit the virtual environment
$ exit

# to run pipeline just run the python file handler.py

```

---

# Contribute
TODO: Explain how other users and developers can contribute to make your code better.

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)

# How to build GlobalConfiguration.xlsx

## Description

The purpose of creating this file is to create a database for the global configuration of OEE sites. Instead of putting the information inside the script, it will be stored in this excel database and the identification code will reference the data from the Cognite Data Model that will store this data. The idea is to have a script that every time an update of the excel file occurs, the data is uploaded to the Data Model configuration for OEE.
## Data Handling

- Download the excel file from OEE_EventFrame repository into Digital Plant project.

- Fill Excel Sheet Columns (1, 2 & 3)

1. OEEReportingSiteConfiguration

Column reportingSiteExternalId: externalId - Retrieve the externalId from the reportingSite table in AssetHierarchy FDM for the specific Site under analysis.

Column reportingSiteSpace: space - Retrieve the Space from the reportingSite table in AssetHierarchy FDM for the specific Site under analysis.

Column shifts: Shift Transition Times - Fill with the shift transition times for the specific Site, as informed by the Site administration team.


2. OEEInputTagConfiguration

Column reportingLineExternalId: externalId - Retrieve the externalId from the reportingLine table in AssetHierarchy FDM for the specific Line under analysis.

Column reportingLineSpace: space - Retrieve the Space from the reportingLine table in AssetHierarchy FDM for the specific Line under analysis.

Column timeSeries: TimeSeries externalId - For each Line, there are some PI tags used to find events. Fill the sheet with the externalId of each TimeSeries searching in DataExplorer of Cognite.

Column alias: alias - Fill with more descriptive names for these TimeSeries, which will be used in the event identification code.

3. OEEEventHierarchyConfiguration

Column eventDefinition: Event Name - Fill with the explicit name of the event ('Not Running', 'Minor Stop', ...).

Column eventHierarchy: Event Code  - Fill with the explicit code of the event ('1a', '1b',...).


Columns subCategoryLevel1,
        eventCode,
        metricCode:
Event Categories - Fill with the categories of the event. If the event depends on variable categories, fill the categories with a '-'.

Column businessRule: businessRule   - This column depends on support from the development team to be filled.

Column workShiftRule: workShiftRule - This column also depends on support from the development team to be filled.

Column reportingLineExternalId: externalId - Retrieve the externalId from the reportingLine table in AssetHierarchy FDM for the specific Line under analysis.

Column reportingLineSpace: space - Retrieve the space from the reportingLine table in AssetHierarchy FDM for the specific Line under analysis.

Column reportingUnitExternalId: externalId - Retrieve the externalId from the reportingUnit table in AssetHierarchy FDM for the specific Unit under analysis.

Column reportingUnitSpace: space - Retrieve the Space from the reportingUnit table in AssetHierarchy FDM for the specific Unit under analysis.