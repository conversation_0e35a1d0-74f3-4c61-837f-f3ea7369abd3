from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class Frac3g8EventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self._msdp = msdp

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "3e": self.identify_events_typeIIIe,
            # "4a": self.identify_events_typeIVa,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus < 50
        data = data.assign(
            event1a_start=(
                ((data["ProductionLineStatus"].shift(1) > 50) & (data["ProductionLineStatus"] < 50)) & ((data["RateTotalizerPI1"] < 10) | (data["RateTotalizerPI2"] < 10))
            )
        )

        # event trigger end - ProductionLineStatus > 50
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus"].shift(1) < 50) & (data["ProductionLineStatus"] > 50)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args):
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Start trigger: (ProductionLineStatus goes from < 20 to > 50) AND (RateTotalizerPI1 < 10 OR RateTotalizerPI2 < 10)
        data = data.assign(
            event2a_start=(
                (
                    data["ProductionLineStatus"].shift(5) < 20) 
                    & (data["ProductionLineStatus"].shift(1) < 20) 
                    & (data["ProductionLineStatus"] > 50
                )
                & ((data["RateTotalizerPI1"] > 10) | (data["RateTotalizerPI2"] > 10))
            )
        )

        # End trigger: (RateTotalizerPI1 > 20 OR RateTotalizerPI2 > 20)
        data = data.assign(
            event2a_end=(
                ((data["ProductionLineStatus"].shift(1) < 20) & (data["RateTotalizerPI1"] > 20)) 
                | ((data["RateTotalizerPI2"].shift(1) < 20) & (data["RateTotalizerPI2"] > 20))
            )
        )

        return data

    def identify_events_typeIIb(self, data: pd.DataFrame, **args):
        """
        identifies the events of type IIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Assign event2b_start using shift for previous row comparison
        data = data.assign(
            event2b_start=(
                (data["ProductionLineStatus"].shift(5) > 50) 
                & (data["ProductionLineStatus"].shift(1) > 50)
                & (data["ProductionLineStatus"] < 20)
            )
        )

        # Assign event2b_end using shift for previous row comparison
        data = data.assign(
            event2b_end=(
                (
                    ((data["RateTotalizerPI1"].shift(1) > 10) & (data["RateTotalizerPI1"] < 10)) 
                    | ((data["RateTotalizerPI2"].shift(1) > 10) & (data["RateTotalizerPI2"] < 10))
                )
            )
        )
    
        return data

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data = self.__create_day_data(data)

        data = data.assign(
            event3a_end=(
                (data["MSDP"] >= data["SCHR"])
                & (data["SCHR"] > data["TotalNetProduction"])
                & (data["TotalNetProduction"] > 10)
                & (data["TotalNetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3a_start=(data["event3a_end"].shift(-1) == True)
        )
        
        data = data.assign(
            total_duration_seconds=(
                (data["SCHR"] - data["TotalNetProduction"]) / (data["SCHR"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        data = self.__create_day_data(data)

        data = data.assign(
            event3b_end=(
                (data["MSDP"] > data["SCHR"])
                & (data["SCHR"] > data["TotalNetProduction"])
                & (data["TotalNetProduction"] > 10)
                & (data["TotalNetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["SCHR"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3c_end=(
                (data["MSDP"] > data["TotalNetProduction"])
                & (data["TotalNetProduction"] > data["SCHR"])
                & (data["TotalNetProduction"] > 10)
                & (data["TotalNetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3c_start=(data["event3c_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["TotalNetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3d_end=(
                (data["TotalNetProduction"] > data["MSDP"])
                & (data["TotalNetProduction"] > 10)
                & (data["TotalNetProduction"] != data["MSDP"])
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3d_start=(data["event3d_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["TotalNetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIe(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIe

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3e_end=(
                (data["MSDP"] > data["SCHR"])
                & (data["SCHR"] == data["TotalNetProduction"])
                & (data["TotalNetProduction"] > 10)
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3e_start=(data["event3e_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["SCHR"]) / (data["MSDP"]/24)
                  ) * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data
    
    # ************************************************************************************************
    # CODE SUSPENSED:
    # User Story v2 142829: Stop Events type 4 (Quality) in IPH 3G8 
    # SUSPENDE EVENTS TYPE 4
    # ************************************************************************************************
    # def identify_events_typeIVa(
    #     self, data: pd.DataFrame, **args
    # ) -> pd.DataFrame:
    #     """
    #     identifies events of type IVa

    #     :param data: dataframe with the events identified
    #     :type data: pd.DataFrame
    #     :return: dataframe with the events of type IV included
    #     :rtype: pd.DataFrame
    #     """

    #     data = self.__create_day_waste_data(data)

    #     data = data.assign(
    #         event4a_end=(
    #             (
    #                 data["WasteTotalizerPI"]
    #                 - data["WasteTotalizerPI"].shift(1)
    #                 < 0
    #             )
    #             & (data["is_midnight"])
    #         )
    #     )

    #     data = data.assign(
    #         event4a_start=(data["event4a_end"].shift(1) == True)
    #     )

    #     data = data.assign(
    #         total_duration_seconds=data["WasteProduced"] * 3600
    #     ).dropna(subset=["total_duration_seconds"])

    #     return data


    def identify_events_typeIVb(self):
        pass
    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        TONNES_MULTIPLICATION_FACTOR = 0.00099

        data["NetProduction"] = (
            data["RateTotalizerPI1"] + data["RateTotalizerPI2"]
        ) * TONNES_MULTIPLICATION_FACTOR

        data["NetProduction1"] = (data["NetProduction"] / 3600) * data["dt"]
        data.set_index("index", inplace=True)
        data["TotalNetProduction"] = (
            data["NetProduction1"].rolling("24h").sum()
        )
        data.reset_index(inplace=True)

        data["is_midnight"] = (
            (data["index"].dt.hour == 0)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        # find the year that is in accordance with the indexed date
        data["Year"] = data["index"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def __create_day_waste_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support event 4a

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type IV included
        :rtype: pd.DataFrame
        """
        TONNES_MULTIPLICATION_FACTOR = 0.00099

        data["WasteTotalizerPI"] = (
            data["WasteTotalizerPI"] * TONNES_MULTIPLICATION_FACTOR
        )

        data["WasteMass"] = (data["WasteTotalizerPI"] / 3600) * data["dt"]
        data.set_index("index", inplace=True)
        data["TotalWaste"] = data["WasteMass"].rolling("24h").sum()
        data.reset_index(inplace=True)

        data["is_midnight"] = (
            (data["index"].dt.hour == 0)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        # find the year that is in accordance with the indexed date
        data["Year"] = data["index"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["index"].dt.month
        data["Day"] = data["index"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_data["msdp"] = msdp_data.apply(
            lambda x: x["scheduledRate"]
            if x["scheduledRate"] > 0
            else x["msdp"],
            axis=1,
        )

        data["MSDP"] = data.apply(
            self.get_msdp_value, msdp_data=msdp_data, data_aux="msdp", axis=1
        )

        data["WasteProduced"] = (data["TotalWaste"]) / (data["MSDP"] / 24)

        return data

    def get_inactive_value(self) -> Optional[str]:
        return None
