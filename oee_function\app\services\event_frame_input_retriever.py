from datetime import datetime, timezone
from typing import List, Optional

import pandas as pd
from app.infra.logger_adapter import get_logger
from app.models.event_frame import EventFrameLastExecutionCollection
from app.models.reporting_site import ReportingSite
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.repositories.bbct_repository import BbctRepository
from app.repositories.event_frame_repository import EventFrameRepository
from app.repositories.mdr_repository import MdrRepository
from app.repositories.msdp_repository import MsdpRepository
from app.repositories.reporting_site_repository import ReportingSiteRepository
from app.repositories.timeseries_repository import TimeSeriesRepository
from dateutil.relativedelta import relativedelta

log = get_logger()


class EventFrameInputRetrieverResponse:
    def __init__(
        self,
        space: str,
        configurations: ReportingSiteConfiguration,
        start_time: datetime,
        end_time: datetime,
        last_execution_metadata: EventFrameLastExecutionCollection,
        bbct_data: pd.DataFrame,
        msdp_data: pd.DataFrame,
        mdr_data: pd.DataFrame,
        reporting_site: ReportingSite,
    ) -> None:
        self.space = space
        self.configurations = configurations
        self.start_time = start_time
        self.end_time = end_time
        self.last_execution_metadata = last_execution_metadata
        self.bbct_data = bbct_data
        self.msdp_data = msdp_data
        self.mdr_data = mdr_data
        self.reporting_site = reporting_site

    space: str
    configurations: ReportingSiteConfiguration
    start_time: datetime
    end_time: datetime
    last_execution_metadata: EventFrameLastExecutionCollection
    bbct_data: pd.DataFrame
    msdp_data: pd.DataFrame
    mdr_data: pd.DataFrame
    reporting_site: ReportingSite


class EventFrameInputRetriever:
    def __init__(
        self,
        reporting_site_repository: ReportingSiteRepository,
        time_series_repository: TimeSeriesRepository,
        bbct_repository: BbctRepository,
        event_frame_repository: EventFrameRepository,
        default_instance_space: str,
        msdp_repository: MsdpRepository,
        mdr_repository: MdrRepository,
    ) -> None:
        self._reporting_site_repository = reporting_site_repository
        self._time_series_repository = time_series_repository
        self._bbct_repository = bbct_repository
        self._event_frame_repository = event_frame_repository
        self._default_instance_space = default_instance_space
        self._msdp_repository = msdp_repository
        self._mdr_repository = mdr_repository

    def retrieve(
        self,
        reporting_site_external_id: str,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        only_lines: List[str],
    ) -> EventFrameInputRetrieverResponse:
        log.info(f"Retrieving configurations for {reporting_site_external_id}")

        configurations = self._reporting_site_repository.get_configurations(
            reporting_site_external_id, only_lines,
        )

        if not configurations:
            raise NotImplementedError(
                f"No configuration for {reporting_site_external_id}"
            )

        ignore_last_execution = start_time is not None

        last_execution = (
            EventFrameLastExecutionCollection()
            if ignore_last_execution
            else (
                self._event_frame_repository.get_events_last_executions(
                    configurations.events_hierarchy,
                    configurations.get_timezone(),
                    configurations.get_shifts_as_timestamp(),
                )
            )
        )

        start_time, end_time = self._get_calculation_period(
            last_execution, start_time, end_time, configurations
        )

        tag_configuration_by_reporting_line = (
            configurations.get_tag_configuration_by_reporting_line()
        )

        reporting_lines = list(tag_configuration_by_reporting_line.keys())
        log.info(f"Retrieving BBCT data for {reporting_lines} ")

        bbct_data = self._bbct_repository.get_bbct_data_as_dataframe(
            reporting_line_external_ids=reporting_lines
        )
        
        log.info(f"Retrieving MSDP data for {reporting_lines} ")
        msdp_data = self._msdp_repository.get_msdp_data_as_dataframe(
            reporting_line_external_ids=reporting_lines,
        )

        log.info(f"Retrieving MDR data for {reporting_lines} ")
        mdr_data = self._mdr_repository.get_mdr_data_as_dataframe(
            reporting_lines
        )

        space = self._get_space(configurations)
        log.info(f"Instance space defined as {space}")

        return EventFrameInputRetrieverResponse(
            space=space,
            configurations=configurations,
            start_time=start_time,
            end_time=end_time,
            last_execution_metadata=last_execution,
            bbct_data=bbct_data,
            msdp_data=msdp_data,
            mdr_data=mdr_data,
            reporting_site=configurations.reporting_site,
        )

    def _get_space(self, configurations: ReportingSiteConfiguration) -> str:
        space = self._default_instance_space
        cor_code = "-COR-"
        if configurations.reporting_site.site_code and cor_code in space:
            site_code = f"-{configurations.reporting_site.site_code}-"
            space = space.replace(cor_code, site_code)
        return space

    def _get_calculation_period(
        self,
        last_execution: EventFrameLastExecutionCollection,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        configurations: ReportingSiteConfiguration
    ) -> tuple[datetime, datetime]:
        if not end_time:
            end_time = datetime.now(timezone.utc)

        if not start_time:
            if configurations.reporting_site.external_id == "STS-WIL":
                start_time = last_execution.get_min_execution() or (
                    datetime.now(timezone.utc) - relativedelta(days=15)
                )
            elif configurations.reporting_site.external_id == "STS-FLO":
                last_execution_flo = last_execution.get_min_execution()
                reference_date = datetime.now(timezone.utc) - relativedelta(days=7)
                
                if last_execution_flo.timestamp() < reference_date.timestamp():
                    start_time = reference_date
                else:
                    start_time = last_execution_flo
                  
            elif configurations.reporting_site.external_id == "STS-PEN":
                last_execution_pen = last_execution.get_min_execution()
                reference_date = datetime.now(timezone.utc) - relativedelta(days=60)
                if last_execution_pen.timestamp() < reference_date.timestamp():
                    start_time = reference_date
                else:
                    start_time = last_execution_pen
            else:
                start_time = last_execution.get_min_execution() or datetime(
                    2023, 1, 1, 0, 0, 0, 0, timezone.utc
                )
        return (start_time, end_time)

    def retrieve_datapoints(
        self,
        configurations: ReportingSiteConfiguration,
        start_time: datetime,
        end_time: datetime,
        reporting_line: str,
    ) -> pd.DataFrame:
        timeseries = configurations.get_timeseries(
            reporting_line=reporting_line
        )
        if not timeseries:
            raise ValueError(f"No timeseries for {reporting_line}")

        log.info(
            f"Retrieving data points for {reporting_line} from {start_time} to {end_time}"
        )

        all_data_cdf = self._time_series_repository.retrieve_dataframe(
            external_ids=timeseries,
            start_time=start_time,
            end_time=end_time,
        )

        timezone = configurations.get_timezone()

        log.info(f"Converting timezone to {timezone}.")

        all_data_cdf.index = all_data_cdf.index.tz_localize("UTC").tz_convert(  # type: ignore
            timezone
        )
        return all_data_cdf
    
    
    def retrieve_timeseries_configurations(
        self,
        configurations: ReportingSiteConfiguration,
        reporting_line: str,
    ) -> pd.DataFrame:
        timeseries = configurations.get_timeseries(
            reporting_line=reporting_line
        )
        if not timeseries:
            raise ValueError(f"No timeseries for {reporting_line}")

        log.info(
            f"Retrieving Timeseries Configurations for {reporting_line}"
        )

        ts_configurations = self._time_series_repository.retrieve_configurations(
            external_ids=timeseries,
        )

        return ts_configurations
