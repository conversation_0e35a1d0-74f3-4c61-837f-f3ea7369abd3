import numpy as np
import pandas as pd
import math
from datetime import datetime, timedelta
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class EventFixingService():
    # Used to delete columns with suffixes after product and events data merge
    COLUMN_PRODUCT = [
        "NetProduction",
        "TotalLoss",
        "TotalFeed",
        "production",
        "total_produced",
        "TotalNetProduction",
        "total_gur_produced",
        "VAMProduced",
        "net_production",
        "material_produced",
        "msdp",
        "MSDP",
        "ScheduledRate",
        "ScheduleRate",
        "scheduledRate",
        "SCHR",
        "ScheduledRateYield",
        "running_time"
    ]

    @staticmethod
    def fix_events_before_save(
        event_frames_df: pd.DataFrame,
        pc_type: str,
        is_historical_data: bool,
        bbct_data: pd.DataFrame,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> pd.DataFrame:
        """
        Applies necessary fixes before saving events, ensuring that specific columns
        are set to None for certain event definitions.
        """
        
        event_frames_df = EventFixingService._set_calculates_fields(event_frames_df, pc_type)
        event_frames_df = EventFixingService._set_none_values_for_conditions(event_frames_df)
        event_frames_df = EventFixingService._fix_duration_by_process_type(event_frames_df, pc_type)
        event_frames_df = EventFixingService._remove_events_in_future(event_frames_df)
        event_frames_df = EventFixingService._adjust_decimal_places(event_frames_df)
        
        event_frames_df = EventFixingService._set_bbct_reference(
            event_frames_df,
            bbct_data,
            reporting_site_configuration)
        
        # if not historical data, remove events older than 30 days
        if not is_historical_data:
            event_frames_df = EventFixingService._keep_only_last_30_days_events(event_frames_df)


        event_frames_df = EventFixingService._order_columns(event_frames_df, pc_type)

        return event_frames_df


    @staticmethod
    def _set_calculates_fields(event_frames_df: pd.DataFrame, pc_type: str) -> pd.DataFrame:
        """
        Sets calculated fields based on the process type.
        """
        if pc_type in {"Continuous", "Continous 2 Products"}:
            if {"MSDP", "NetProduction"}.issubset(event_frames_df.columns):
                # Total lost production = MSDP - NetProduction
                event_frames_df["total_lost_production"] = (
                    event_frames_df["MSDP"] - event_frames_df["NetProduction"]
                ).round(1)
                
                
                event_frames_df["lostProductionMT"] = (event_frames_df["total_duration_seconds"] ) * event_frames_df["MSDP"]/24
            
            

        elif pc_type == "Compounding":
            if {"MDR", "total_duration_seconds", "mdr_unit"}.issubset(event_frames_df.columns):
                event_frames_df["lostProductionMT"] = np.where(
                    event_frames_df["mdr_unit"] == "kg/h",
                    (event_frames_df["total_duration_seconds"] / 3600) * event_frames_df["MDR"] / 1000,
                    (event_frames_df["total_duration_seconds"] / 3600) * event_frames_df["MDR"] / 2205
                )

        # Aplica net_production_lbs se NetProduction existir
        if "NetProduction" in event_frames_df.columns:
            event_frames_df["net_production_lbs"] = (
                event_frames_df["NetProduction"] * 2204.62
            ).round(1)

        return event_frames_df

    @staticmethod
    def _set_bbct_reference(
        event_frames_df: pd.DataFrame,
        bbct_data: pd.DataFrame,
        reporting_site_configuration: ReportingSiteConfiguration
    ) -> pd.DataFrame:
        """
        Create BBCT reference for each event frame based on the productId, reportingLineExternalId and timestamp.
        This method checks if the event definition and reporting line depend on BBCT, filters the BBCT data accordingly,
        and assigns the most recent externalId and space to the event frame.

        For each line in the event frames, it checks if the event definition is in the list of event definitions that depend on BBCT.
        If it does, it filters the BBCT data based on the productId, reportingLineExternalId, and dateSet.
        If a match is found, it assigns the externalId and space to the refOEEBBCT column in the event frames DataFrame.
        Args:
            event_frames_df (pd.DataFrame): DataFrame containing event frames.
            bbct_data (pd.DataFrame): DataFrame containing BBCT data.
            reporting_site_configuration (ReportingSiteConfiguration): Configuration object for the reporting site.
        Returns:
            pd.DataFrame: Updated event frames DataFrame with refOEEBBCT column set.
        Note:
            - The refOEEBBCT column is initialized to None and will be updated with the externalId and space from the BBCT data.
            - The method assumes that the bbct_data DataFrame contains columns: productId, reportingLineExternalId, dateSet, externalId, and space.
            - The event_frames_df DataFrame is expected to have columns: event_definition, Product, ProductDescription, refReportingLineId, start_time.
        """

        event_frames_df["refOEEBBCT"] = None  # Initialize the column to None
        
        if  bbct_data is None or bbct_data.empty:
            return event_frames_df
        
        bbct_data["dateSet"] = pd.to_datetime(bbct_data["dateSet"], errors="coerce")

        for index, row in event_frames_df.iterrows():
            event_definition = row.get("event_definition")
            product_id = row.get("Product")
            product_description = row.get("ProductDescription")
            reporting_line = row.get("refReportingLineId")
            start_time = row.get("start_time")
            loss_time = row.get("total_duration_seconds", None)

            if pd.isna(product_id) or pd.isna(event_definition) or pd.isna(start_time):
                continue

            # Check if the event definition is in the list of event definitions that depend on BBCT
            if not reporting_site_configuration.event_dependends_on_bbct(
                reporting_line, event_definition
            ):
                continue

            # 🔍 Find de BBCT register
            filter_bbct = (
                (
                    (bbct_data["productId"] == product_id) |  
                    (bbct_data["productId"] == product_description) | 
                    (bbct_data["productId"] == product_description.lower() if product_description else False) | 
                    (bbct_data["productId"] == product_description.upper() if product_description else False) 
                ) &
                (bbct_data["reportingLineExternalId"] == reporting_line) &
                (bbct_data["dateSet"] <= start_time)
            )

            aux = bbct_data.loc[filter_bbct].copy()

            if aux.empty:
                continue

            # Sort by dateSet to get the most recent entry
            aux.sort_values(by="timestamp", ascending=False, inplace=True)

            # Create Column refOEEBBCT with the externalId and space in Dataframe
            event_frames_df.at[index, "refOEEBBCT"] = {
                "externalId": aux.iloc[0]["externalId"],
                "space": aux.iloc[0]["space"]
            }
            
            if loss_time is not None:
                event_frames_df.at[index, "lostProductionMT"] = (loss_time / 60 / 60) * aux.iloc[0]["bestBatchCycleTimeMT"] / aux.iloc[0]["bestBatchCycleTimeHr"]
            
            


        return event_frames_df


    @staticmethod
    def _order_columns(event_frames_df: pd.DataFrame, pc_type: str) -> pd.DataFrame:
        """
        Orders the columns of the DataFrame to a predefined order.
        """
        # Define the desired column order
        desired_order = []
        if pc_type == "Batch":
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "running_time", "total_duration_seconds", "refOEEBBCT", "NetProduction", "net_production_lbs",
                "lostProductionMT", "total_loss_production", "status", "isOpen", "isManual", "isDeleted",
                "MSDP", "ScheduledRate",
            ]
        elif pc_type == "Compounding":
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "running_time", "total_duration_seconds", "lostProductionMT", "MDR", "mdr_unit",
                "total_loss_production", "NetProduction", "net_production_lbs",
                "status", "isOpen", "isManual", "isDeleted", "MSDP", "ScheduledRate", "refOEEBBCT",
            ]
        else:
            desired_order = [
                "externalId", "refReportingLineId", "event_definition", "start_time", "end_time", "def",
                "NetProduction", "net_production_lbs", "MSDP", "ScheduledRate", "running_time", 
                "total_duration_seconds", "status", "isOpen", "isManual", "isDeleted", 
                "refOEEBBCT", "lostProductionMT", "total_loss_production"
            ]
        
        desired_existing = [col for col in desired_order if col in event_frames_df.columns]
        other_cols = [c for c in event_frames_df.columns if c not in desired_existing]
        ordered_cols = desired_existing + other_cols

        event_frames_df = event_frames_df[ordered_cols]

        return event_frames_df

    @staticmethod
    def _set_none_values_for_conditions(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Sets specific columns to None based on event definitions that are NOT in the exclusion list.
        """
        # Event definitions to exclude
        excluded_event_definitions = {
            "Excess MDR",
            "Low Production",
            "Running Above MDR",
            "Running Under MDR",
            "Running Above MDR (Riser)",
            "Running Under MDR (Riser)",
            "Running Under Scheduled Rate",
            "No Demand"
        }

        # Columns to set as None when condition is met
        columns_to_nullify = ["NetProduction", "net_production_lbs", "MSDP", "ScheduledRate", "running_time", "total_loss_production"]

        # Create mask for rows where 'def' is NOT in the exclusion list
        mask = ~event_frames_df["def"].isin(excluded_event_definitions)

        # Apply None values to the selected columns
        event_frames_df.loc[mask, columns_to_nullify] = None

        return event_frames_df

    @staticmethod
    def _fix_duration_by_process_type(event_frames_df: pd.DataFrame, pc_type: str) -> pd.DataFrame:
        """
        Fixes the duration of events based on the process type.
        """
        if pc_type == "Continuous":
            return EventFixingService._fix_duration_for_continuous_process(event_frames_df)
       
        return event_frames_df

    @staticmethod
    def _fix_duration_for_continuous_process(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Fixes the duration of events for continuous processes.
        """
        
        event_frames_df["start_time"] = pd.to_datetime(event_frames_df["start_time"])
        event_frames_df["end_time"] = pd.to_datetime(event_frames_df["end_time"])

        # If endtime - starttime is more than 1 day, set it to 1 day
        mask = (event_frames_df["end_time"] - event_frames_df["start_time"]).dt.days > 0
        event_frames_df.loc[mask, "end_time"] = event_frames_df.loc[mask, "start_time"] + pd.Timedelta(days=1)

        return event_frames_df
    
    @staticmethod
    def event_frame_column_renaming_mapper(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardizes column names by mapping various used aliases 
        to a set of predefined column names.
        """

        net_production_column_alias = {
            "NetProduction": "NetProduction",
            "TotalFeed": "NetProduction",
            "production": "NetProduction",
            "total_produced": "NetProduction",
            "TotalNetProduction": "NetProduction",
            "total_gur_produced": "NetProduction",
            "VAMProduced": "NetProduction",
            "net_production": "NetProduction",
            "material_produced": "NetProduction"
        }

        msdp_column_alias = {
            "MSDP": "MSDP",
            "msdp": "MSDP",
        }

        sr_column_alias = {
            "ScheduledRate": "ScheduledRate",
            "ScheduleRate": "ScheduledRate",
            "scheduledRate": "ScheduledRate",
            "SCHR": "ScheduledRate",
            "ScheduledRateYield": "ScheduledRate"
        }

        running_time_column_alias = {
            "running_time": "running_time"
        }

        rename_mapper = {**net_production_column_alias, **msdp_column_alias, **sr_column_alias, **running_time_column_alias}

        event_frames_df.rename(columns=rename_mapper, inplace=True)

        return event_frames_df

    @staticmethod
    def fix_minor_stops_within_starting_up(df):
        # PT-BR: Agrupa o DataFrame por refReportingLineId
        # EN: Group the DataFrame by refReportingLineId
        grouped = df.groupby('refReportingLineId')

        processed_dfs = []

        # PT-BR: Itera sobre os grupos
        # EN: Iterates over the groups
        for line_id, line_df in grouped:
            # PT-BR: Filtra os registros "Starting up" para a linha atual
            # EN: Filters the "Starting up" records for the current line
            starting_up = line_df[line_df['def'] == 'Starting Up']

            # PT-BR: Função para verificar se um "Minor Stops" está dentro de algum "Starting up"
            # EN: Function to check if a "Minor Stops" is within any "Starting up"
            def is_within_starting_up(row):
                for _, su_row in starting_up.iterrows():
                    if su_row['start_time'] <= row['start_time'] <= su_row['end_time'] or \
                        su_row['start_time'] <= row['end_time'] <= su_row['end_time'] or \
                        (row['start_time'] <= su_row['start_time'] and row['end_time'] >= su_row['end_time']):
                        return True
                return False

            mask = line_df['def'] == 'Minor Stops'
            line_df = line_df[~(mask & line_df.apply(is_within_starting_up, axis=1))]
            processed_dfs.append(line_df)

        df = pd.concat(processed_dfs, ignore_index=True)
        return df

    @staticmethod
    def adjust_batch_loss_for_batch_hold(df: pd.DataFrame, event_hierarchy: list):
        """
        For each 'Batch Loss Time' event, subtracts the overlapping duration of any 'Batch Hold' events.
        Uses event_hierarchy to determine which event codes to use.
        """
        df = df.copy()
        df["batch_hold_overlap_seconds"] = 0.0

        # Find event codes for Batch Loss Time and Batch Hold
        batch_loss_codes = [e.event_hierarchy for e in event_hierarchy if e.event_definition == "Batch Loss Time"]
        batch_hold_codes = [e.event_hierarchy for e in event_hierarchy if e.event_definition == "Batch Hold"]

        batch_loss_events = df[df['event_definition'].isin(batch_loss_codes)].copy()
        batch_hold_events = df[df['event_definition'].isin(batch_hold_codes)].copy()

        # Iterate over each batch loss event
        for bl_idx, bl_row in batch_loss_events.iterrows():
            bl_start = bl_row['start_time']
            bl_end = bl_row['end_time']
            overlap = 0.0

            # Iterate over each batch hold and filter events that overlap with the current batch loss event
            for _, bh_row in batch_hold_events.iterrows():
                bh_start = bh_row['start_time']
                bh_end = bh_row['end_time']

                # Calculate overlap
                latest_start = max(bl_start, bh_start)
                earliest_end = min(bl_end, bh_end)
                delta = (earliest_end - latest_start).total_seconds()
                if delta > 0:
                    overlap += delta

            # Update the batch_loss row
            batch_loss_events.at[bl_idx, "batch_hold_overlap_seconds"] = overlap
            if "total_duration_seconds" in batch_loss_events.columns:
                orig_duration = batch_loss_events.at[bl_idx, "total_duration_seconds"]
                batch_loss_events.at[bl_idx, "total_duration_seconds"] = orig_duration - overlap

        df.drop(columns=["batch_hold_overlap_seconds"], inplace=True)

        df.update(batch_loss_events)
        return df

    @staticmethod
    def _remove_events_in_future(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Removes events when event in the future.
        """
        end_time_tz = event_frames_df["end_time"].dt.tz

        mask = event_frames_df["end_time"] > pd.Timestamp.now().tz_localize("UTC").tz_convert(end_time_tz)
        event_frames_df = event_frames_df[~mask]
        
        mask = event_frames_df["start_time"] > pd.Timestamp.now().tz_localize("UTC").tz_convert(end_time_tz)
        event_frames_df = event_frames_df[~mask]
        
        return event_frames_df



    @staticmethod
    def _keep_only_last_30_days_events(event_frame_df: pd.DataFrame) -> pd.DataFrame:
        """
        Keeps only events whose start_time is within the last 30 days.
        """
        # Detecte the timezone of the start_time column
        tz = event_frame_df["start_time"].dt.tz

        # Apply the same timezone to the cutoff_date
        cutoff_date = pd.Timestamp(datetime.now(), tz=tz) - pd.Timedelta(days=30)

        return event_frame_df[event_frame_df["start_time"] >= cutoff_date].copy()




    @staticmethod
    def _adjust_decimal_places(event_frames_df: pd.DataFrame) -> pd.DataFrame:
        """
        Adjusts DataFrame columns decimal places.
        """
        adjust_columns = ["MSDP", "ScheduledRate", "running_time", "NetProduction", "lostProductionMT"]
        decimal_columns = {
            "decimal": {
                "MSDP": 1,
                "ScheduledRate": 1,
                "NetProduction": 2,
                "total_loss_production": 2,
                "net_production_lbs": 2,
                "running_time": 1,
                "lostProductionMT": 2
            },
            "round": {
                "MSDP": False,
                "ScheduledRate": False,
                "NetProduction": False,
                "total_loss_production": False,
                "net_production_lbs": False,
                "running_time": True,
                "lostProductionMT": True
            }
        }
        
        event_frames_df = event_frames_df[~event_frames_df.isin([np.inf, -np.inf]).any(axis=1)]
        
        for col in event_frames_df.columns:
            if col in adjust_columns:
                decimal_places = decimal_columns["decimal"].get(col, 2)
                should_round = decimal_columns["round"].get(col, False)
                
                if should_round:
                    factor = 10 ** decimal_places
                    event_frames_df[col] = event_frames_df[col].apply(
                        lambda x: math.ceil(x * factor) / factor if pd.notna(x) and (x * factor) % 1 >= 0.1 else round(x, decimal_places) if pd.notna(x) else x
                    )
                else:
                    factor = 10 ** decimal_places
                    event_frames_df[col] = event_frames_df[col].apply(lambda x: math.trunc(x * factor) / factor if pd.notna(x) else x)

        return event_frames_df