from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class BisPARAFORMEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """
        # Event trigger start
        # ProductionLineStatus1 = 0
        event_start = self.not_running_condition_PFM(data)
        # Event trigger end
        # ProductionLineStatus2 >= 165
        data["ProductionLineStatus2"] = pd.to_numeric(data["ProductionLineStatus2"], errors='coerce')
        event_end = (data["ProductionLineStatus2"] >= 165) & (data["ProductionLineStatus2"].shift(1) < 165)

        data = data.assign(
            event1a_start=event_start, event1a_end=event_end
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        Identifies events type IIa

        """
        data["ProductionLineStatus1"] = pd.to_numeric(data["ProductionLineStatus1"], errors='coerce')
        data["ProductionLineStatus2"] = pd.to_numeric(data["ProductionLineStatus1"], errors='coerce')
        
        # Event trigger start
        # ProductionLineStatus1 goes from 0 to >= 200
        event_start = (data["ProductionLineStatus1"].shift(1) <= 0) & (data["ProductionLineStatus1"] >= 200)

        # Event trigger end
        # ProductionLineStatus1 > 600
        event_end = (data["ProductionLineStatus1"].shift(1) <= 600) & (data["ProductionLineStatus1"] > 600)

        data = data.assign(
            event2a_start=event_start, event2a_end=event_end
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
            self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (data["MSDP"] - data["NetProduction"]) / (data["MSDP"] /24)

        data = data.assign(
            event3a_end=(
                (data["MSDP"] >=data["NetProduction"])
            )
        )

        data = data.assign(
            event3a_start=(data["event3a_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(data["RLT"]) * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data["RLT"] = (data["MSDP"] - data["NetProduction"]) / (data["MSDP"] /24)

        data = data.assign(
            event3c_end=(
                (data["MSDP"] < data["NetProduction"])
                & (data["NetProduction"] > 0)
                & (data["is_midnight"])
            )
        )

        data = data.assign(
            event3c_start=(data["event3c_end"].shift(-1) == True)
        )

        data = data.assign(
            total_duration_seconds=(data["RLT"]) * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_inactive_value(self) -> Optional[str]:
        return None
    
    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data.set_index("index", inplace=True)

        date_max = data.index.max()
        date_min = data.index.min()

        indices = pd.date_range(
            data.index[0].floor("D"),
            data.index[-1].floor("D") + pd.DateOffset(days=1),
            freq="1h",
        )

        data = data.reindex(
            pd.concat([data.index.to_series(), indices.to_series()])
        )

        data.sort_index(inplace=True)

        data = data[
            (data.index >= date_min) & (data.index <= date_max)
        ]

        dtypes = data.dtypes

        data.ffill(inplace=True)

        for column in data.columns:
            data[column] = data[column].astype(dtypes[column])

        data.reset_index(inplace=True)

        # drop duplicates to remove work shift index duplicated
        data = data.drop_duplicates("index", keep="first")

        data["NetProduction"] = (data['NetProduction']) / 1000

        running_time_key = "running_time"

        not_running_condition = self.not_running_condition_PFM(data)

        data["condition_running_time"] = not_running_condition

        data["duration_in_seconds_without_condition"] = (data["index"].diff().dt.total_seconds().fillna(0))

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                "NetProduction": "mean",
                "duration_in_seconds": "sum",
                "ProductionLineStatus1": "mean",
            }
        )
        
        data = data.reset_index().rename(columns={"index": "timestamp"})

        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month
        data["Day"] = data["timestamp"].dt.day

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )

        data["MSDP"] = data["MSDP"] / 24
        
        data["SCHR"] = data["SCHR"] / 24

        data = (
            data.groupby(
                [pd.Grouper(key="timestamp", freq="D"), data["ProductDescription"]],
            )
            .agg(
                {
                    "MSDP": "sum",
                    "SCHR": "sum",
                    "NetProduction": "mean",
                    "running_time": "sum",
                },
            )
            .reset_index()
        )

        data["is_midnight"] = (
            (data["timestamp"].dt.hour == 00)
            & (data["timestamp"].dt.minute == 0)
            & (data["timestamp"].dt.second == 0)
            & (data["timestamp"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
            data = data[data["is_midnight"]]

        data = data.rename(columns={"timestamp": "index"})

        return data
    
    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)
    
    def not_running_condition_PFM(self, data: pd.DataFrame) -> pd.Series:

        data["ProductionLineStatus1"] = pd.to_numeric(data["ProductionLineStatus1"], errors='coerce')
        
        return(
            (data["ProductionLineStatus1"] <= 165) & (data["ProductionLineStatus1"].shift(1) > 165)
        )
