from datetime import time
from typing import Any, List, Union

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class NanGurEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "2d": self.identify_events_typeIId,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        start_key = "event1a_start"
        end_key = "event1a_end"

        data[start_key] = (
            (data["TotalFeed"] < 500)
        )

        data[end_key] = (
            (data["TotalFeed"] > 500)
        )

        data[start_key] = (data[start_key]) & (data[start_key].shift(1) != True)
        data[end_key] = (data[end_key]) & (data[end_key].shift(1) != True)

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self, data: pd.DataFrame, **args):
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        start_key = "event2a_start"
        end_key = "event2a_end"

        data["timestamp"] = pd.to_datetime(data.iloc[:, 0])
        data["line_status_sum"] = data["ProductionLineStatus_1"] + data["ProductionLineStatus_2"]
        data["production_line_status_1_2_48h"] = (
            data.rolling(window="2D", min_periods=1, on="timestamp")["line_status_sum"]
            .apply(lambda x: (x < 1000).all())
            .astype(bool)
        )

        # Ensure there is at least 2 days of data before setting event2a_start
        data["sufficient_history"] = (
            data["timestamp"] - data["timestamp"].iloc[0] >= pd.Timedelta(days=2)
        )

        data[start_key] = (
            (data["line_status_sum"] >= 550) &
            (data["line_status_sum"] < 5500) &
            (data["production_line_status_1_2_48h"].shift(1)) &
            data["sufficient_history"] # Ensure there's at least 2 days of data
        )

        data[start_key] = (data[start_key]) & (data[start_key].shift(1) != True)

        data[end_key] = False

        # End time is set to 2 days after the start time
        for start_time in data.loc[data[start_key], "timestamp"]:
            end_time = start_time + pd.Timedelta(days=2)
            idx = data["timestamp"].searchsorted(end_time)
            if idx < len(data):
                data.at[data.index[idx], end_key] = True

        data[end_key] = (data[end_key]) & (data[end_key].shift(1) != True)

        # Drop the temporary column
        data = data.drop(columns=["sufficient_history"])

        return data

    def identify_events_typeIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies the events of type IIb

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        data = data.assign(
            event2b_start=(
                (data["TotalFeed"] > 500)
                & (data["Product"].astype(str).str.startswith("X"))
            )
        )

        data = data.assign(
            event2b_end=(
                (
                    (data["TotalFeed"].shift(1) > 500)
                    & (data["TotalFeed"] < 500)
                ) 
                | (~data["Product"].astype(str).str.startswith("X"))
            )
        )

        # fix event trigger start
        data["event2b_start"] = (
            data["event2b_start"] & (data["event2b_start"].shift(1) != True)
        )

        return data

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIId(self):
        pass

    def identify_events_typeIIIa(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self.__create_day_data(data)

        day_data = day_data[(day_data[self._rlt_key] > 0) & day_data[self._rlt_key].apply(lambda x: x != float('inf'))]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIb(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self.__create_day_data(data)

        day_data = day_data[(day_data[self._rlt_key] < 0) & day_data[self._rlt_key].apply(lambda x: x != float('-inf'))]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIc(self, data: pd.DataFrame, **args) -> pd.DataFrame:
        day_data = self.__create_day_data(data)

        day_data = day_data[(day_data[self._rlt_no_demand_key] > 0) & day_data[self._rlt_no_demand_key].apply(lambda x: x != float('inf'))]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        # NOTE: logic below is for 12 pm to 12 pm intervals (not 12 am to 12 am)
        # mid_day_time = time(12, 0, 0, 0)
        # first_timestamp = data["index"].head(1).iloc[0]
        # last_timestamp = data["index"].tail(1).iloc[0]
        # start_cutoff = (
        #     None
        #     if (ft_time := first_timestamp.time()) == mid_day_time
        #     else (
        #         first_timestamp.replace(hour=12, minute=0, second=0, microsecond=0)
        #         if ft_time < mid_day_time
        #         else (first_timestamp + pd.Timedelta(days=1)).replace(
        #             hour=12, minute=0, second=0, microsecond=0
        #         )
        #     )
        # )
        # end_cutoff = (
        #     None
        #     if (lt_time := last_timestamp.time()) == mid_day_time
        #     else (
        #         last_timestamp.replace(hour=12, minute=0, second=0, microsecond=0)
        #         if lt_time > mid_day_time
        #         else (last_timestamp + pd.Timedelta(days=1)).replace(
        #             hour=12, minute=0, second=0, microsecond=0
        #         )
        #     )
        # )
        # data = data[
        #     ((start_cutoff is not None) & (data["index"] >= start_cutoff))
        #     & ((end_cutoff is not None) & (data["index"] < end_cutoff))
        # ]
        #     # se a hora for menor que 12, o cutoff tem que ser as 12h do dia atual
        #     # se a hora for maior que 12, o cutoff tem que ser as 12h do dia seguinte

        # inicio: se o time for diferente de meia noite, tira aquele dia inteiro
        # final: se o time for meia noite, pega tudo até ele (<), se ele for diferente de meia noite
        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None if first_timestamp.time() == mid_night_time else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            ((start_cutoff is not None) & (data["index"].dt.date > start_cutoff))
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        total_gur_produced_key = "total_gur_produced"

        data[total_gur_produced_key] = (
            data["TotalFeed"] * 0.985
        )

        per_hour_to_per_sec_factor = 1 / 3600
        kg_to_ton_factor = 1 / 1000

        data[total_gur_produced_key] = (
            data[total_gur_produced_key]
            * kg_to_ton_factor
            * per_hour_to_per_sec_factor
            * data["dt"]
        )

        is_below_500_key = "is_below_500"
        is_above_500_key = "is_above_500"

        data[is_below_500_key] = (
            (data["TotalFeed"] < 500)
        )

        data[is_above_500_key] = (
            (data["TotalFeed"] > 500)
        )

        running_time_key = "running_time"

        data[running_time_key] = 0

        sec_to_hour_factor = 1 / 3600

        data.loc[data[is_above_500_key], running_time_key] = (
            data.loc[data[is_above_500_key], "dt"] * sec_to_hour_factor
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "Product"], dropna=False
        ).agg(
            {
                is_below_500_key: "all",
                is_above_500_key: "all",
                total_gur_produced_key: "sum",
                running_time_key: "sum",
            }
        )
        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})

        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data = data.loc[data.groupby("timestamp")["total_gur_produced"].idxmax()]

        data_to_get_products_from = data.copy().drop(
            labels=[col for col in data.columns if col not in ["timestamp", "Product"]],
            axis=1,
        )

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        filter_msdp = (
            self._msdp["reportingUnitExternalId"] == self._reporting_unit_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        scheduled_rate_key = "scheduledRate"

        per_day_to_per_hour_factor = 1 / 24

        data[[msdp_key, scheduled_rate_key]] = (
            data.apply(
                self.get_msdp_value,
                msdp_data=msdp_data,
                data_aux=[msdp_key, scheduled_rate_key],
                axis=1,
                result_type="expand",
            )
            * per_day_to_per_hour_factor
        )

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001

        rlt_schema = [
            (  # 3a
                "3a",
                lambda df: (df[is_below_500_key]),  # is below 500 during hour
                {rlt_key: lambda _: 0},
            ),
            (
                "3b",
                lambda df: (df[total_gur_produced_key] - df[msdp_key]).abs()
                < tolerance,  # produced = msdp
                {rlt_key: lambda _: 0},
            ),
            (
                "3c1",
                lambda df: ((df[total_gur_produced_key] - df[msdp_key]) > tolerance)
                & (
                    df[is_above_500_key]
                ),  # produced > msdp & total feed is above 500 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_gur_produced_key])
                    / df[msdp_key]
                },
            ),
            (
                "3c2",
                lambda df: ((df[total_gur_produced_key] - df[msdp_key]) > tolerance)
                & (
                    df[is_above_500_key] == False
                ),  # produced > msdp & total feed is not above 500 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_gur_produced_key]
                    )
                    / df[msdp_key]
                },
            ),
            (
                "3d1",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & ((df[scheduled_rate_key] - df[total_gur_produced_key]) > tolerance)
                & (
                    df[is_above_500_key]
                ),  # msdp > scheduled & scheduled > GUR produced & is above 500 during hour
                {
                    rlt_key: lambda df: (
                        df[scheduled_rate_key] - df[total_gur_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[scheduled_rate_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3d2",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & ((df[scheduled_rate_key] - df[total_gur_produced_key]) > tolerance)
                & (
                    df[is_above_500_key] == False
                ),  # msdp > scheduled & scheduled > GUR produced & is not above 500 during hour
                {
                    rlt_key: lambda df: (
                        (df[scheduled_rate_key] * df[running_time_key])
                        - df[total_gur_produced_key]
                    )
                    / df[scheduled_rate_key],
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key])
                        - (df[scheduled_rate_key] * df[running_time_key])
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3e1",
                lambda df: ((df[msdp_key] - df[total_gur_produced_key]) > tolerance)
                & ((df[total_gur_produced_key] - df[scheduled_rate_key]) > tolerance)
                & (
                    df[is_above_500_key]
                ),  # msdp > GUR produced & GUR produced > scheduled & is above 500 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[total_gur_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3e2",
                lambda df: ((df[msdp_key] - df[total_gur_produced_key]) > tolerance)
                & ((df[total_gur_produced_key] - df[scheduled_rate_key]) > tolerance)
                & (
                    df[is_above_500_key] == False
                ),  # msdp > GUR produced & GUR produced > scheduled & is not above 500 during hour
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_gur_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3f1",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]).abs() < tolerance)
                & ((df[scheduled_rate_key] - df[total_gur_produced_key]) > tolerance)
                & (
                    df[is_above_500_key]
                ),  # msdp = GUR produced & scheduled > GUR produced & is above 500 during hour
                {
                    rlt_key: lambda df: (df[msdp_key] - df[total_gur_produced_key])
                    / df[msdp_key],
                },
            ),
            (
                "3f2",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]).abs() < tolerance)
                & ((df[scheduled_rate_key] - df[total_gur_produced_key]) > tolerance)
                & (
                    df[is_above_500_key] == False
                ),  # msdp = GUR produced & scheduled > GUR produced & is not above 500 during hour
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] * df[running_time_key]) - df[total_gur_produced_key]
                    )
                    / df[msdp_key],
                },
            ),
            (
                "3g",
                lambda df: ((df[msdp_key] - df[scheduled_rate_key]) > tolerance)
                & (
                    (df[scheduled_rate_key] - df[total_gur_produced_key]).abs() < tolerance
                ),  # msdp > scheduled & scheduled = GUR produced
                {
                    rlt_no_demand_key: lambda df: (
                        df[msdp_key] - df[scheduled_rate_key]
                    )
                    / df[msdp_key],
                },
            )
        ]

        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")

        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(data.loc[filter_mask])
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)

        data = data[data[msdp_key] > 0.1]

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    scheduled_rate_key: "sum",
                    total_gur_produced_key: "sum",
                    rlt_key: "sum",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )

        # no events should be generated if total_produced <= 0
        data = data[data[total_gur_produced_key] > 0]

        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )

        # data.loc[(~data["Product"].isin(product_list)), "Product"] = pd.NA

        # get msdp and scheduled rate on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day
        data[[msdp_key, scheduled_rate_key]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=[msdp_key, scheduled_rate_key],
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_gur_produced_key] - data[msdp_key]).abs() < tolerance)
            & ((data[total_gur_produced_key] - data[scheduled_rate_key]).abs() < tolerance)
            & ((data[msdp_key] - data[scheduled_rate_key]).abs() < tolerance)
        )

        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(columns=["Product"])

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = data[rlt_no_demand_key] * hours_to_seconds_factor

        self._day_data = data

        return data.copy()

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: Union[str, List[str]]
    ) -> Union[float, pd.Series]:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float or pd.Series
        """
        # Handle special case for NA products
        product = row.get("Product")
        if pd.isna(product):
            return pd.array([0] * len(data_aux)) if isinstance(data_aux, list) else 0

        # Create product filter
        product_filter = msdp_data["productGroup"] == product

        # Use the utility function with the product filter
        return msdp_util_get_msdp_value(row, msdp_data, data_aux, product_filter)
