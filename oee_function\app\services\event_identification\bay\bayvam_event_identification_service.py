from typing import Any, Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as utils_get_msdp_value


class BAYVAMEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """
        # event trigger start - ProductionLineStatus < 140
        data = data.assign(event1a_start=(data["ProductionLineStatus"] < 140))

        # event trigger end - ProductionLineStatus > 140
        data = data.assign(event1a_end=(data["ProductionLineStatus"] > 140))

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        ).assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        identification_columns = ["NetProduction", "ProductionLineStatus"]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )
        data = self.__create_day_data(data=data, col="NetProduction")
        
        data = data.assign(
            event2a_end=(
                (data["RLT"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["running_time"] > 0.25)
            )
        )

        # data = data.assign(
        #     event2a_start=(data["event2a_end"].shift(-1) == True)
        # )
        
        data = data.assign(
            event2a_start=(data["event2a_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        identification_columns = ["NetProduction", "ProductionLineStatus"]
        data = self.__remove_unecessary_data(
            data=data, columns_to_keep=identification_columns
        )

        data = self.__create_day_data(data=data, col="NetProduction")

        data = data.assign(
            event2b_end=(
                (data["RLT"] < 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["running_time"] > 0.25)
            )
        )

        # data = data.assign(
        #     event2b_start=(data["event2b_end"].shift(-1) == True)
        # )
        data = data.assign(
            event2b_start=(data["event2b_end"])
        )

        data = data.assign(total_duration_seconds=data["RLT"] * 3600).dropna(
            subset=["total_duration_seconds"]
        )

        return data

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(self):
        pass

    def identify_events_typeIIIc(self):
        pass

    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame, col: str) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        LBS_TO_MT_DIVISION_FACTOR = 2204.62

        data[col] = data[col] / LBS_TO_MT_DIVISION_FACTOR        
        
        running_time_key = "running_time"
        not_running_condition = (data["ProductionLineStatus"] < 140)
        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )
        
        data["is_midnight"] = (
            (data["index"].dt.hour == 0)
            & (data["index"].dt.minute == 0)
            & (data["index"].dt.second == 0)
            & (data["index"].dt.microsecond == 0)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="D")],
            dropna=False,
        ).agg(
            {
                col: "first",
                "ProductionLineStatus": "mean",
                "duration_in_seconds": "sum",
            }
        )
    
        # data = data.drop(data.index[-1]) # REMOVE LAST LINE OF DATAFRAME

        # reset multiindex, timestamp and product become columns
        data["timestamp"] = pd.to_datetime(data.index)
        data = data[data["timestamp"].dt.date != pd.Timestamp.today().date()]


        
        data[running_time_key] = data["duration_in_seconds"]/3600
        
        data.drop(columns=["duration_in_seconds"], inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year
 
        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month
        data["Day"] = data["timestamp"].dt.day

        data = data.reset_index()

        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )
        
        # if col == "NetProduction":
        #     data["NetProduction"] = data["NetProduction"].shift(1)
        
        # If net production <= 0 and the running time is greater than 15min, set the production to 0 and generate event
        data.loc[(data[col] <= 0) & (data[running_time_key] >= 0.25), col] = 0

        data["RLT"] = ((data["MSDP"] * data[running_time_key] / 24) - data["NetProduction"]) / (
            data["MSDP"] / 24
        )
        
        data = data.drop(data.index[0]).reset_index(drop=True)
                
        return data

    def get_msdp_value(
        self,
        row: pd.Series,
        msdp_data: pd.DataFrame,
        data_aux: str,
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract without product filtering
        """
        return utils_get_msdp_value(row, msdp_data, data_aux)

    def get_inactive_value(self) -> Optional[str]:
        return None

    def __remove_unecessary_data(
        self, data: pd.DataFrame, columns_to_keep: list[str]
    ) -> pd.DataFrame:
        remove_columns = [
            col
            for col in data.columns.to_list()
            if col not in columns_to_keep + ["index"]
        ]
        return data.drop(remove_columns, axis=1).dropna(
            subset=columns_to_keep, how="all"
        )
