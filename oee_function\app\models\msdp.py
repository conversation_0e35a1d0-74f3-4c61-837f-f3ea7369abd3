from datetime import date
from typing import Any, Optional

from pydantic import Field, computed_field

from .material import Material
from .node import Node
from .reporting_line import ReportingLine
from .reporting_site import ReportingSite
from .unit_of_measurement import UnitOfMeasurement


class Msdp(Node):
    reporting_site: Optional[ReportingSite] = Field(alias="refSite")
    reporting_line: Optional[ReportingLine] = Field(
        default=None, alias="refReportingLine"
    )
    effective_date: str = Field(default=None, alias="effectiveDate")
    uom: Optional[UnitOfMeasurement] = Field(alias="uom")
    product_group: str = Field(default=None, alias="productGroup")
    scheduled_rate: Optional[float] = Field(
        default=None, alias="scheduledRate"
    )
    refMaterial: Optional[Material] = Field(default=None, alias="refMaterial")
    msdp: float = Field(default=None, alias="msdp")
    mssr: Optional[float] = Field(default=None, alias="mssr")
    created_by: str = Field(default=None, alias="createdBy")
    modified_by: str = Field(default=None, alias="modifiedBy")

    @computed_field
    @property
    def effective_date_datetime(self) -> date:
        return date.fromisoformat(self.effective_date)

    @computed_field
    @property
    def day(self) -> int:
        return self.effective_date_datetime.day
    
    @computed_field
    @property
    def month(self) -> int:
        return self.effective_date_datetime.month

    @computed_field
    @property
    def year(self) -> int:
        return self.effective_date_datetime.year
    
    @computed_field
    @property
    def day(self) -> int:
        return self.effective_date_datetime.day

    @classmethod
    def from_cognite_response(cls, item: dict[str, Any]) -> "Msdp":
        return Msdp(
            externalId=item["externalId"],
            space=item["space"],
            refSite=ReportingSite(**item["refSite"]),
            refReportingLine=ReportingLine(**item["refReportingLine"])
            if (item.get("refReportingLine"))
            else None,
            effectiveDate=item["effectiveDate"],
            uom=UnitOfMeasurement(**item["uom"]),
            productGroup=item["productGroup"] if item["productGroup"] else "",
            refMaterial=Material(**item["refMaterial"]) if item["refMaterial"] else None,
            scheduledRate=item["scheduledRate"],
            msdp=item["msdp"],
            mssr=item["mssr"],
        )
