import logging
import os
import sys
from ast import literal_eval

import pandas as pd
from cognite.client.data_classes.data_modeling.data_models import DataModelId
from core.cognite_client_factory import CogniteClientFactory
from core.env_variables import EnvVariables

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


from oee_function.app.models.bbct import Bbct
from oee_function.app.models.business_segment import BusinessSegment
from oee_function.app.models.country import Country
from oee_function.app.models.mdr import Mdr
from oee_function.app.models.msdp import Msdp
from oee_function.app.models.region import Region
from oee_function.app.models.reporting_line import ReportingLine
from oee_function.app.models.reporting_site import ReportingSite
from oee_function.app.models.material import Material
from oee_function.app.models.reporting_site_configuration import (
    EventHierarchyConfiguration,
    InputTagConfiguration,
    ReportingSiteConfiguration,
)
from oee_function.app.models.reporting_unit import ReportingUnit
from oee_function.app.models.timeseries_configuration import (
    TimeseriesConfiguration,
)
from oee_function.app.models.unit_of_measurement import UnitOfMeasurement
from oee_function.app.repositories.bbct_repository import BbctRepository
from oee_function.app.repositories.mdr_repository import MdrRepository
from oee_function.app.repositories.msdp_repository import MsdpRepository
from oee_function.app.repositories.reporting_site_repository import (
    ReportingSiteRepository,
)
from oee_function.app.repositories.timeseries_configuration_repository import (
    TimeseriesConfigurationRepository,
)
from oee_function.app.repositories.view_repository import ViewRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SHEET_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    "data",
    "GlobalConfiguration.xlsx",
)


def read_spreeadsheet(sheet_name: str) -> pd.DataFrame:
    return pd.read_excel(SHEET_PATH, sheet_name=sheet_name)


def run_reporting_site_configuration(
    instance_space: str,
) -> list[ReportingSiteConfiguration]:
    data = read_spreeadsheet("OEEReportingSiteConfiguration")

    data["fixEventNoVal"] = data["fixEventNoVal"].fillna("False")
    data["regexProcessing"] = data["regexProcessing"].fillna("")
    data["extraEventProcessing"] = data["extraEventProcessing"].fillna("")
    data["extraTimeProcessing"] = data["extraTimeProcessing"].fillna("")

    configurations = data.to_dict(orient="records")
    result: list[ReportingSiteConfiguration] = []
    for configuration in configurations:
        reporting_site_external_id = configuration.get(
            "reportingSiteExternalId"
        )
        reporting_site_space = configuration.get("reportingSiteSpace")
        shift_value = configuration.get("shifts", "")
        fix_null_values_subset_value = configuration.get(
            "fixNullValuesSubset", ""
        )

        shifts = literal_eval(shift_value)
        fix_null_values_subset = literal_eval(fix_null_values_subset_value)

        extra_time_processing = configuration.get("extraTimeProcessing")
        result.append(
            ReportingSiteConfiguration(
                externalId=f"OEERSC-{reporting_site_external_id}",
                space=instance_space,
                reportingSite=ReportingSite(
                    externalId=reporting_site_external_id,
                    space=reporting_site_space,
                ),
                shifts=shifts,
                extraEventProcessing=configuration.get("extraEventProcessing"),
                extraTimeProcessing=(
                    extra_time_processing
                    if extra_time_processing != ""
                    else None
                ),
                regexProcessing=configuration.get("regexProcessing"),
                fixEventNoVal=configuration.get("fixEventNoVal"),
                lineType=configuration.get("lineType"),
                fixNullValuesSubset=fix_null_values_subset,
            )
        )

    return result


def run_input_tag_configuration(
    instance_space: str,
) -> list[InputTagConfiguration]:
    data = read_spreeadsheet("OEEInputTagConfiguration")
    data["eventIdentification"] = data["eventIdentification"].fillna("False")
    data["tagValueMapping"] = data["tagValueMapping"].fillna("False")
    input_tags = data.to_dict(orient="records")
    result: list[InputTagConfiguration] = []
    for input_tag in input_tags:
        reporting_line_external_id = input_tag.get("reportingLineExternalId")
        reporting_line_space = input_tag.get("reportingLineSpace")
        timeseries = input_tag.get("timeSeries")
        alias = input_tag.get("alias")
        event_identification = input_tag.get("eventIdentification")
        tag_value_mapping = input_tag.get("tagValueMapping")
        result.append(
            InputTagConfiguration(
                externalId=f"OEEITC-{reporting_line_external_id}-{alias}",
                space=instance_space,
                alias=alias,
                timeSeries=timeseries,
                reportingLine=ReportingLine(
                    externalId=reporting_line_external_id,
                    space=reporting_line_space,
                ),
                eventIdentification=event_identification,
                tagValueMapping=tag_value_mapping,
            )
        )

    return result


def run_event_hierarchy_configuration(
    instance_space: str,
) -> list[EventHierarchyConfiguration]:
    data = read_spreeadsheet("OEEEventHierarchyConfiguration")
    data["workShiftRule"] = data["workShiftRule"].fillna("False")
    data["usesBbct"] = data["usesBbct"].fillna("False")
    data["variableCategories"] = data["variableCategories"].fillna("False")
    data["notRunningRule"] = data["notRunningRule"].fillna("False")
    data["minorStop"] = data["minorStop"].fillna("False")
    data["notRunningRule"] = data["notRunningRule"].fillna("False")
    data["businessRule"] = data["businessRule"].fillna("False")
    data.fillna("", inplace=True)
    events_hierarchy = data.to_dict(orient="records")

    result: list[EventHierarchyConfiguration] = []
    for event in events_hierarchy:
        reporting_line_external_id = event.get("reportingLineExternalId")
        reporting_line_space = event.get("reportingLineSpace")
        event_definition = event.get("eventDefinition")
        event_hierarchy = event.get("eventHierarchy")
        eh_shift_value = event.get("eventHierarchyShifts", "")

        if eh_shift_value:
            try:
                eh_shifts = literal_eval(eh_shift_value)
            except (ValueError, SyntaxError) as e:
                print(f"There's no eventHierarchyShift: {e}")
                eh_shifts = None
        else:
            eh_shifts = None

        external_id = f"OEEEHC-{reporting_line_external_id}-{event_definition}-{event_hierarchy}"
        result.append(
            EventHierarchyConfiguration(
                externalId=external_id,
                space=instance_space,
                reportingLine=(
                    ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    )
                    if reporting_line_external_id
                    else None
                ),
                **{
                    k: v
                    for k, v in event.items()
                    if k != "eventHierarchyShifts"
                },
                eventHierarchyShifts=eh_shifts,
            )
        )

    return result


def run_timeseries_configuration(
    instance_space: str,
) -> list[TimeseriesConfiguration]:
    data = read_spreeadsheet("OEETimeseriesConfiguration")

    data["isStep"] = data["isStep"].fillna("False")
    data["isString"] = data["isString"].fillna("False")
    data.replace({float("nan"): None}, inplace=True)

    configs = data.to_dict(orient="records")

    result: list[TimeseriesConfiguration] = []
    for config in configs:
        reporting_site_external_id = config.get("reportingSiteExternalId")
        reporting_site_space = config.get("reportingSiteSpace")
        reporting_unit_external_id = config.get("reportingUnitExternalId")
        reporting_unit_space = config.get("reportingUnitSpace")
        reporting_line_external_id = config.get("reportingLineExternalId")
        reporting_line_space = config.get("reportingLineSpace")
        uom_external_id = config.get("uomExternalId")
        uom_space = config.get("uomSpace")

        timeseries_external_id = config.get("timeseriesExternalId")
        external_id = f"OEET-{reporting_line_external_id.split('-')[1]}-{timeseries_external_id.split(':')[1]}"

        site_code = f"-{reporting_site_external_id.split('-')[1]}-"
        cor_code = "-COR-"
        site_space = instance_space.replace(cor_code, site_code)

        result.append(
            TimeseriesConfiguration(
                externalId=external_id,
                space=site_space,
                reportingSite=ReportingSite(
                    externalId=reporting_site_external_id,
                    space=reporting_site_space,
                ),
                reportingUnit=ReportingUnit(
                    externalId=reporting_unit_external_id,
                    space=reporting_unit_space,
                ),
                reportingLine=ReportingLine(
                    externalId=reporting_line_external_id,
                    space=reporting_line_space,
                ),
                uom=UnitOfMeasurement(
                    externalId=uom_external_id,
                    space=uom_space,
                ),
                **config,
            )
        )

    return result


def create_cognite_client(variables: EnvVariables):
    return CogniteClientFactory.create(variables)


def run_bbct_configuration(
    instance_space: str,
) -> list[Bbct]:
    bbct_raw = read_spreeadsheet("OEEBBCT")

    bbct_preprocessed = (
        bbct_raw.assign(
            dateSet=lambda df: pd.to_datetime(
                df.dateSet, format="%m/%d/%Y %I:%M %p", errors="coerce"
            ).fillna(
                pd.to_datetime(
                    df.dateSet, format="%m/%d/%Y  %H:%M:%S", errors="coerce"
                )
            )
        )
        .assign(dateSet=lambda df: df.dateSet.dt.strftime("%Y-%m-%dT%H:%M:%S"))
        .assign(
            reportingLineExternalId=lambda df: df.reportingLineExternalId.str.strip()
        )
        .astype({"pITagValue": str})
        .dropna(axis=1, how="all")
        .fillna("")
        .replace({"bestBatchCycleTimeMT": "", "active": "", "leadBBCT": ""}, None)
        .drop_duplicates()
        .reset_index(drop=True)
        .reset_index()
    )

    bbct_records = bbct_preprocessed.to_dict(orient="records")

    result: list[Bbct] = []
    for bbct in bbct_records:
        reporting_line_external_id = bbct.get("reportingLineExternalId")
        reporting_line_space = bbct.get("reportingLineSpace")
        reporting_site_external_id = bbct.get("reportingSiteExternalId")
        reporting_site_space = bbct.get("reportingSiteSpace")

        index = bbct.get("index")
        external_id = f"OEEB-052924-A{'{:04d}'.format(index + 1)}"

        site_code = f"-{reporting_site_external_id.split('-')[1]}-"
        cor_code = "-COR-"
        site_space = instance_space.replace(cor_code, site_code)

        product_name = bbct.get("refProduct")

        material_external_id = bbct.get("Material ID")
        material_space = "SAP-COR-ALL-DAT"

        result.append(
            Bbct(
                externalId=external_id,
                space=site_space,
                refSite=(
                    ReportingSite(
                        externalId=reporting_site_external_id,
                        space=reporting_site_space,
                    )
                    if reporting_site_external_id
                    else None
                ),
                refReportingLine=(
                    ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    )
                    if reporting_line_external_id
                    else None
                ),
                product=product_name,
                refMaterial=(
                    Material(
                        externalId=material_external_id,
                        space=material_space,
                    )
                    if material_external_id
                    else None
                ),
                **bbct,
            )
        )

    return result


def run_msdp_configuration(
    instance_space: str,
) -> list[Msdp]:
    values = {
        "geoRegionExternalId": "",
        "geoRegionSpace": "",
        "countryExternalId": "",
        "countrySpace": "",
        "reportingSiteExternalId": "",
        "reportingSiteSpace": "",
        "reportingUnitExternalId": "",
        "reportingUnitSpace": "",
        "businessSegmentExternalId": "",
        "businessSegmentSpace": "",
        "Month": 0,
        "Year": 0,
        "msdp": 0,
        "uomExternalId": "",
        "uomSpace": "",
        "productGroup": "",
        "scheduledRate": 0,
    }
    msdp_raw = read_spreeadsheet("OEEMSDP")
    msdp_raw["Month"] = msdp_raw["Month"].astype(str).str.zfill(2)
    msdp_preprocessed = (
        msdp_raw.assign(
            effectiveDate=lambda df: df["Year"].astype(str)
            + "-"
            + df["Month"]
            + "-"
            + "01"
        )
        .astype({"productGroup": str, "scheduledRate": float})
        .fillna({"scheduledRate": 0})
        .drop_duplicates(
            subset=[
                "reportingUnitExternalId",
                "reportingLineExternalId",
                "Month",
                "Year",
                "msdp",
                "mssr",
                "productGroup",
                "scheduledRate",
            ]
        )
        .dropna(subset=["msdp"])
        .dropna(axis=1, how="all")
        .replace({float("nan"): None})
    )

    msdp_records = msdp_preprocessed.to_dict(orient="records")

    result: list[Msdp] = []
    for msdp in msdp_records:
        business_segment_external_id = msdp.get("businessSegmentExternalId")
        business_segment_space = msdp.get("businessSegmentSpace")
        reporting_site_external_id = msdp.get("reportingSiteExternalId")
        reporting_site_space = msdp.get("reportingSiteSpace")
        region_external_id = msdp.get("geoRegionExternalId")
        region_space = msdp.get("geoRegionSpace")
        country_external_id = msdp.get("countryExternalId")
        country_space = msdp.get("countrySpace")
        reporting_unit_external_id = msdp.get("reportingUnitExternalId")
        reporting_unit_space = msdp.get("reportingUnitSpace")
        reporting_line_external_id = msdp.get("reportingLineExternalId")
        reporting_line_space = msdp.get("reportingLineSpace")
        uom_external_id = msdp.get("uomExternalId")
        uom_space = msdp.get("uomSpace")

        external_id = (
            f"OEEM-{reporting_unit_external_id.split('-')[1]}"
            f"{('-' + reporting_line_external_id.split('-')[1]) if reporting_line_external_id else ''}"
            f"-{msdp.get('Year')}"
            f"-{msdp.get('Month')}"
            f"-{msdp.get('productGroup')}"
            f"-{str(msdp.get('msdp'))}"
            f"-{str(msdp.get('scheduledRate'))}"
        ).replace(" ", "")

        site_code = f"-{reporting_site_external_id.split('-')[1]}-"
        cor_code = "-COR-"
        site_space = instance_space.replace(cor_code, site_code)

        result.append(
            Msdp(
                externalId=external_id,
                space=site_space,
                refSite=(
                    ReportingSite(
                        externalId=reporting_site_external_id,
                        space=reporting_site_space,
                    )
                    if reporting_site_external_id
                    else None
                ),
                refBusinessSegment=(
                    BusinessSegment(
                        externalId=business_segment_external_id,
                        space=business_segment_space,
                    )
                    if business_segment_external_id
                    else None
                ),
                refRegion=(
                    Region(
                        externalId=region_external_id,
                        space=region_space,
                    )
                    if region_external_id
                    else None
                ),
                refCountry=(
                    Country(
                        externalId=country_external_id,
                        space=country_space,
                    )
                    if country_external_id
                    else None
                ),
                refUnit=(
                    ReportingUnit(
                        externalId=reporting_unit_external_id,
                        space=reporting_unit_space,
                    )
                    if reporting_unit_external_id
                    else None
                ),
                refReportingLine=(
                    ReportingLine(
                        externalId=reporting_line_external_id,
                        space=reporting_line_space,
                    )
                    if reporting_line_external_id
                    else None
                ),
                uom=(
                    UnitOfMeasurement(
                        externalId=uom_external_id,
                        space=uom_space,
                    )
                    if uom_external_id
                    else None
                ),
                **msdp,
            )
        )

    return result


def run_mdr_configuration(
    instance_space: str,
) -> list[Mdr]:
    mdr_raw = read_spreeadsheet("OEEMDR")

    mdr_preprocessed = (
        mdr_raw.assign(
            dateSet=lambda df: df.dateSet.dt.strftime("%Y-%m-%dT%H:%M:%S")
        )
        .astype({"productFamily": int})
        .astype({"productFamily": str})
        .dropna(axis=1, how="all")
        .fillna("")
        .drop_duplicates()
        .reset_index(drop=True)
        .reset_index()
    )

    mdr_records = mdr_preprocessed.to_dict(orient="records")

    result: list[Mdr] = []
    for mdr in mdr_records:
        reporting_line_external_id = mdr.get("reportingLineExternalId")
        reporting_line_space = mdr.get("reportingLineSpace")
        reporting_site_external_id = mdr.get("reportingSiteExternalId")
        reporting_site_space = mdr.get("reportingSiteSpace")
        region_external_id = mdr.get("regionExternalId")
        region_space = mdr.get("regionSpace")
        country_external_id = mdr.get("countryExternalId")
        country_space = mdr.get("countrySpace")
        uom_external_id = mdr.get("uomExternalId")
        uom_space = mdr.get("uomSpace")
        business_segment_external_id = mdr.get("businessSegmentExternalId")
        business_segment_space = mdr.get("businessSegmentSpace")
        reporting_unit_external_id = mdr.get("reportingUnitExternalId")
        reporting_unit_space = mdr.get("reportingUnitSpace")
        region_space = mdr.get("regionSpace")
        index = mdr.get("index")
        external_id = f"OEEMADR-061824-A{'{:04d}'.format(index + 1)}"
        site_code = f"-{reporting_site_external_id.split('-')[1]}-"
        cor_code = "-COR-"
        site_space = instance_space.replace(cor_code, site_code)

        result.append(
            Mdr(
                externalId=external_id,
                space=site_space,
                refSite=ReportingSite(
                    externalId=reporting_site_external_id,
                    space=reporting_site_space,
                )
                if reporting_site_external_id
                else None,
                refRegion=Region(
                    externalId=region_external_id, space=region_space
                )
                if region_external_id
                else None,
                refCountry=Country(
                    externalId=country_external_id, space=country_space
                )
                if country_external_id
                else None,
                refUnitOfMeasurement=UnitOfMeasurement(
                    externalId=uom_external_id, space=uom_space
                )
                if uom_external_id
                else None,
                refBusinessSegment=BusinessSegment(
                    externalId=business_segment_external_id,
                    space=business_segment_space,
                )
                if business_segment_external_id
                else None,
                refUnit=ReportingUnit(
                    externalId=reporting_unit_external_id,
                    space=reporting_unit_space,
                )
                if reporting_unit_external_id
                else None,
                refReportingLine=ReportingLine(
                    externalId=reporting_line_external_id,
                    space=reporting_line_space,
                )
                if reporting_line_external_id
                else None,
                **mdr,
            )
        )

    return result


def create_repositories(variables: EnvVariables):
    cognite_client = create_cognite_client(variables)
    data_model_id = DataModelId(
        variables.cognite.data_model_space,
        variables.cognite.data_model_external_id,
        variables.cognite.data_model_version,
    )

    return {
        "reporting_site": ReportingSiteRepository(
            cognite_client,
            ViewRepository(cognite_client, data_model_id),
            data_model_id,
        ),
        "timeseries_configuration": TimeseriesConfigurationRepository(
            cognite_client,
            ViewRepository(cognite_client, data_model_id),
            data_model_id,
        ),
        "bbct": BbctRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
        "msdp": MsdpRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
        "mdr": MdrRepository(
            cognite_client,
            data_model_id,
            view_repository=ViewRepository(cognite_client, data_model_id),
        ),
    }


def get_configurations(variables: EnvVariables):
    repository = create_repositories(variables)
    return repository.get("reporting_site").get_reporting_site("STS-ENR")


def run():
    variables = EnvVariables()
    instance_space = variables.cognite.default_data_model_instances_space

    events_hierarchy = run_event_hierarchy_configuration(instance_space)
    input_tags = run_input_tag_configuration(instance_space)
    # timeseries_configuration = run_timeseries_configuration(instance_space)
    # bbct = run_bbct_configuration(instance_space)
    # msdp = run_msdp_configuration(instance_space)
    # mdr = run_mdr_configuration(instance_space)

    repositories = create_repositories(variables)
    reporting_site_repository = repositories.get("reporting_site")
    # timeseries_configuration_repository = repositories.get(
    #     "timeseries_configuration"
    # )
    # bbct_repository = repositories.get("bbct")
    # msdp_repository = repositories.get("msdp")
    # mdr_repository = repositories.get("mdr")

    for reporting_site in run_reporting_site_configuration(instance_space):
        site_code = reporting_site.reporting_site.external_id.replace(
            "STS-", ""
        )
        reporting_line_code = f"RLN-{site_code}"

        reporting_site.events_hierarchy = [
            item
            for item in events_hierarchy
            if item.reporting_line.external_id.startswith(reporting_line_code)
        ]
        reporting_site.input_tags = [
            item
            for item in input_tags
            if item.reporting_line.external_id.startswith(reporting_line_code)
        ]
        reporting_site_repository.create_configuration(reporting_site)

        # timeseries_configs = [
        #     item
        #     for item in timeseries_configuration
        #     if item.reporting_line.external_id.startswith(reporting_line_code)
        # ]
        # timeseries_configuration_repository.create_configuration(
        #     timeseries_configs
        # )

        # bbcts = [
        #     item
        #     for item in bbct
        #     if item.reporting_line.external_id.startswith(reporting_line_code)
        # ]
        # bbct_repository.create_bbct(bbcts)

        # msdps = [
        #     item
        #     for item in msdp
        #     if item.reporting_unit.external_id.startswith(reporting_unit_code)
        # ]
        # msdp_repository.create_msdp(msdps)

        # mdrs = [
        #     item
        #     for item in mdr
        #     if item.reporting_line.external_id.startswith(reporting_line_code)
        # ]
        # mdr_repository.create_mdr(mdrs)

    data = get_configurations(variables)

    assert data is not None


if __name__ == "__main__":
    run()
