from typing import Any, Optional

import pandas as pd
from app.enums.inactive_value_enum import InactiveValueEnum
from app.models.reporting_site_configuration import ReportingSiteConfiguration


class DefaultBatchEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
    ) -> None:
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_site_configuration = reporting_site_configuration
        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pl.DataFrame
        """

        # first form of event start - BatchID = '' OR BatchID = Inactive for more than 2 seconds
        data = data.assign(
            event1a_start=(
                (
                    (data["BatchID"] == "")
                    & (data["dt"].shift(-1) > 2)
                    & (data["BatchID"].shift(1) != "")
                )
                | (
                    (data["BatchID"] == "Inactive")
                    & (data["dt"].shift(-1) > 2)
                    & (
                        data["BatchID"].shift(1)
                        != "Inactive"
                    )
                )
            )
        )

        # event end - BatchID is not Inactive and last BatchID is Inactive
        data = data.assign(
            event1a_end=(data["BatchID"] != "Inactive")
            & (data["BatchID"] != "")
            & (data["BatchID"].isin(args["valid_batch_ids"]))
            & (
                (data["BatchID"].shift(1) == "Inactive")
                | (data["BatchID"].shift(1) == "")
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(self):
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pl.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pl.DataFrame
        """

        # Creating triggers placeholders.
        data = data.assign(event3b_start=False, event3b_end=False)

        data = data.assign(
            BatchID_bool=(
                (data["BatchID"].isin(args["valid_batch_ids"]))
                & (data["BatchID"] != "")
            )
        )

        data = data.assign(
            BatchID_text_bool=(
                ~(data["BatchID"].isin(args["valid_batch_ids"]))
                & (data["BatchID"] != "Inactive")
            )
        )

        # Initialize active event flag
        active_event = False
        batch_active = None

        # data = data.reset_index()
        # Iterate over the lines and update 'start' and 'end'
        for idx in range(0, len(data)):
            # conditions

            condition_start = (data["BatchID_bool"][idx] == True) & (
                data["BatchID"][idx] != batch_active
            )
            if idx + 1 < len(data):
                condition_end_1 = (
                    data["BatchID"][idx] == ""
                ) & (data["dt"][idx + 1] > 300)
                condition_end_2 = (
                    data["BatchID"][idx] == "Inactive"
                ) & (data["dt"][idx + 1] > 300)

            else:
                condition_end_1 = False
                condition_end_2 = False

            condition_end_3 = (data["BatchID_text_bool"][idx] == True) & (
                data["BatchID"][idx] != "Inactive"
            )
            condition_end_4 = (data["BatchID_bool"][idx] == True) & (
                data["BatchID"][idx] != batch_active
            )

            # If there is no active event and the conditions are met, mark as 'start'
            if not active_event and condition_start:
                data["event3b_start"][idx] = True

                # Mark that an event is active
                active_event = True

                # if data[args['col_name']][idx] != args['code_inactive']:
                batch_active = data["BatchID"][idx]  # isso não funciona
                # else:
                #     batch_active = batch_active

                condition_end_4 = (data["BatchID_bool"][idx] == True) & (
                    data["BatchID"][idx] != batch_active
                )

            # If there is an active event and conditions of end is done, mark it as 'end'
            if active_event and (
                (condition_end_1)
                | (condition_end_2)
                | (condition_end_3)
                | (condition_end_4)
            ):
                data["event3b_end"][idx] = True
                active_event = False  # Reset the active event flag
                # batch_active = None

            # If there is no active event and the conditions are met, mark as 'start'
            if not active_event and condition_start:
                data["event3b_start"][idx] = True

                batch_active = data["BatchID"][idx]

                # condition_end_4 = (data['BatchID_bool'][idx] == True) & (data[args['col_name']][idx] != batch_active)
                # Mark that an event is active
                active_event = True

        data = data.drop(["BatchID_bool", "BatchID_text_bool"], axis=1)

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pl.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pl.DataFrame
        """

        # Creating triggers placeholders.
        data = data.assign(event3c_start=False, event3c_end=False)

        data = data.assign(
            BatchID_bool=(
                (data["BatchID"].isin(args["valid_batch_ids"]))
                & (data["BatchID"] != "")
            )
        )

        data = data.assign(
            BatchID_text_bool=(
                ~(data["BatchID"].isin(args["valid_batch_ids"]))
                & (data["BatchID"] != "Inactive")
            )
        )

        # Initialize active event flag
        active_event = False
        batch_active = None

        # Iterate over the lines and update 'start' and 'end'
        for idx in range(0, len(data)):
            # conditions

            condition_start = (data["BatchID_bool"][idx] == True) & (
                data["BatchID"][idx] != batch_active
            )
            if idx + 1 < len(data):
                condition_end_1 = (
                    data["BatchID"][idx] == ""
                ) & (data["dt"][idx + 1] > 300)
                condition_end_2 = (
                    data["BatchID"][idx] == "Inactive"
                ) & (data["dt"][idx + 1] > 300)

            else:
                condition_end_1 = False
                condition_end_2 = False

            condition_end_3 = (data["BatchID_text_bool"][idx] == True) & (
                data["BatchID"][idx] != "Inactive"
            )
            condition_end_4 = (data["BatchID_bool"][idx] == True) & (
                data["BatchID"][idx] != batch_active
            )

            # If there is no active event and the conditions are met, mark as 'start'
            if not active_event and condition_start:
                data["event3c_start"][idx] = True

                # Mark that an event is active
                active_event = True

                batch_active = data["BatchID"][idx]  # isso não funciona

                condition_end_4 = (data["BatchID_bool"][idx] == True) & (
                    data["BatchID"][idx] != batch_active
                )

            # If there is an active event and conditions of end is done, mark it as 'end'
            if active_event and (
                (condition_end_1)
                | (condition_end_2)
                | (condition_end_3)
                | (condition_end_4)
            ):
                data["event3c_end"][idx] = True
                active_event = False  # Reset the active event flag

            # If there is no active event and the conditions are met, mark as 'start'
            if not active_event and condition_start:
                data["event3c_start"][idx] = True

                batch_active = data["BatchID"][idx]

                # Mark that an event is active
                active_event = True

        data = data.drop(["BatchID_bool", "BatchID_text_bool"], axis=1)

        return data
    
    def identify_events_typeIVa(self):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_bbct_value(self, row: pd.Series, bbct_data: pd.DataFrame) -> tuple[float, float]:
        """
        retrieves the BBCT from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: Tuple with BBCT and leadBBCT values
        :rtype: tuple[float, float]
        """
        # extract filter parameter
        prod_id = row["Product"]
        event = row["event_definition"]
        start_time_year = row["start_time"].year
        start_time_month = row["start_time"].month

        # create reference date to find BBCT
        ref_date = pd.to_datetime(f"{start_time_year}-{start_time_month}-1")

        # if the event does not depend on BBCT values, return 0
        if not self._reporting_site_configuration.event_dependends_on_bbct(
            self._reporting_line_external_id,
            event,
        ):
            return 0, 0

        # first filter - filter by year and month of the start date and
        # by the productid
        filter_1 = (
            (bbct_data["productId"] == prod_id)
            & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            & (bbct_data["year"] == start_time_year)
            & (bbct_data["month"] == start_time_month)
        )
        aux = bbct_data.loc[filter_1, :]

        # if aux is empty, we need to test the second filter
        if aux.shape[0] == 0:
            filter_2 = (bbct_data["productId"] == prod_id) & (
                bbct_data["reportingLineExternalId"]
                == self._reporting_line_external_id
            )
            aux = bbct_data.loc[filter_2, :]

            # if still the aux is empty, then we don't have matches, return 0
            if aux.shape[0] == 0:
                return 0, 0

            # ensure ordering by the most recent year, considering the event start date
            t = (aux["timestamp"] - ref_date).abs().values
            aux.loc[:, "diff_dates"] = t
            aux.sort_values(by=["diff_dates"], inplace=True, ascending=False)

            # fill values to get the most recent date preceding the date of event
            aux.fillna(method="ffill", inplace=True)
            aux["bbct"].fillna(0, inplace=True)
            aux["leadBBCT"].fillna(0, inplace=True)

            # ensure ordering by the most recent year, considering the event start date
            aux.sort_values(by=["diff_dates"], inplace=True)

        # extract value of MDR
        return aux["bbct"].head(1).values[0], aux["leadBBCT"].head(1).values[0]

    def fix_3b_duration_based_BBCT(
        self, data: pd.DataFrame, bbct_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        applies the business that demands subtraction of the
        Best Batch Cycle time from every event of type 3b

        :param data: event data
        :type data: pd.DataFrame
        :return: data with the right duration of the events
        :rtype: pd.DataFrame
        """
        # apply extraction of the BBCT value
        data[["bbct", "leadBBCT"]] = pd.DataFrame(
            data.apply(lambda row: self.get_bbct_value(row, bbct_data), axis=1).tolist(),
            index=data.index
        )

        # fix events 3b duration (only where event_definition == "3b")
        data.loc[data["event_definition"] == "3b", "total_duration_seconds"] -= data["bbct"]

        # Fix events 3c duration (apply only if leadBBCT != bbct, otherwise set to 0)
        mask_3c = (data["event_definition"] == "3c") & (data["bbct"] != data["leadBBCT"] )
        data.loc[mask_3c, "total_duration_seconds"] = data["bbct"] - data["leadBBCT"]
        data.loc[~mask_3c & (data["event_definition"] == "3c"), "total_duration_seconds"] = 0  # Explicitly set to 0

        # Set total_duration_seconds to 0 when event is 3c and condition is not met
        data.loc[data["event_definition"] == "3c", "total_duration_seconds"] = data["total_duration_seconds"].fillna(0)

        # fill NaN values with 0 when data does not have BBCT
        data["total_duration_seconds"].fillna(0, inplace=True)

        # events which products does not have bbct is reported with duration zero
        data.loc[
            ((data["event_definition"] == "3b") & (data["bbct"] == 0)),
            "total_duration_seconds",
        ] = 0

        data = data.query("total_duration_seconds != 0")

        # drop created column
        data.drop(columns=["bbct"], inplace=True)

        return data

    def get_inactive_value(self) -> Optional[str]:
        return InactiveValueEnum.INACTIVE.value
