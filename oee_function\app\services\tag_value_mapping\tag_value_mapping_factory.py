from app.services.tag_value_mapping.bay.bayvam_product_description_mapping_service import (
  BAYVAMProductDescriptionMappingService,
)
from app.services.tag_value_mapping.bis.bis_chemicals_product_description_mapping_service import (
  BisChemicalsMO3ProductDescriptionMappingService,
  BisChemicalsMO4ProductDescriptionMappingService,
  BisChemicalsPRFMProductDescriptionMappingService,
)
from app.services.tag_value_mapping.bis.bis_gur_product_mapping_service import (
  BisGurProductMappingService,
)
from app.services.tag_value_mapping.bis.bis_production_line_status_mapping_service import (
  BisProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.can.can_ac20_product_description_mapping_service import (
  CanAc20ProductDescriptionMappingService,
)
from app.services.tag_value_mapping.can.can_eta_product_description_mapping_service import (
  CanETAProductDescriptionMappingService,
)
from app.services.tag_value_mapping.can.can_mea_product_description_mapping_service import (
  CanMeaProductDescriptionMappingService,
)
from app.services.tag_value_mapping.can.can_meo_product_description_mapping_service import (
  CanMeoProductDescriptionMappingService,
)
from app.services.tag_value_mapping.can.can_mibk_product_description_mapping_service import (
  CanMibkProductDescriptionMappingService,
)
from app.services.tag_value_mapping.clk.clkaa_product_description_mapping_service import (
  CLKAAProductDescriptionMappingService,
)
from app.services.tag_value_mapping.clk.clkms1_product_description_mapping_service import (
  CLKMS1ProductDescriptionMappingService,
)
from app.services.tag_value_mapping.clk.clkvam_product_description_mapping_service import (
  CLKVAMProductDescriptionMappingService,
)
from app.services.tag_value_mapping.eva.eva_production_line_status_mapping_service import (
  EvaProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.flo.flo_production_line_status_mapping_service import (
  FloProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.fra.fra_compounding_production_line_status_mapping_service import (
  FraCompondingProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.fra.frac_pom_product_description_mapping_service import (
  FracPomProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fraeus_3G8_product_description_mapping_service import (
  FraEus3G8ProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fraeus_crh_product_description_mapping_service import (
  FraEusCRHProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fraeus_dbm_product_description_mapping_service import (
  FraEusDBMProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fraeus_ply_product_description_mapping_service import (
  FraEusPlyProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fravam_ach_product_description_mapping_service import (
  FraVamAchProductDescriptionMappingService,
)
from app.services.tag_value_mapping.fra.fravam_vam_product_description_mapping_service import (
  FraVamVamProductDescriptionMappingService,
)
from app.services.tag_value_mapping.gel.gel_vae_production_line_status_mapping_service import (
  GelVaeProductionLineDescriptionMappingService,
)
from app.services.tag_value_mapping.nan.nan_hac_product_description_mapping_service import (
  NanHacProductDescriptionMappingService,
)
from app.services.tag_value_mapping.nan.nan_production_line_status_mapping_service import (
  NanProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.nan.nan_vam_product_description_mapping_service import (
  NanVAMProductDescriptionMappingService,
)
from app.services.tag_value_mapping.nan.nan_gur_product_mapping_service import (
  NanGurProductMappingService,
)
from app.services.tag_value_mapping.nar.nar_product_description_mapping_service import (
  NarProductDescriptionMappingService,
)
from app.services.tag_value_mapping.npt.npt_production_line_status_mapping_service import (
  NptProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.obh.obh_product_mapping_service import (
  ObhProductMappingService,
)
from app.services.tag_value_mapping.shy.shy_product_description_mapping_service import (
  ShyProductDescriptionMappingService,
)
from app.services.tag_value_mapping.sps.sps_est_product_description_mapping_service import (
  SpsEstProductDescriptionMappingService,
)
from app.services.tag_value_mapping.sps.sps_est_product_mapping_service import (
  SpsEstProductMappingService,
)
from app.services.tag_value_mapping.sps.sps_hac_product_description_mapping_service import (
  SpsHacProductDescriptionMappingService,
)
from app.services.tag_value_mapping.sps.sps_vam_product_description_mapping_service import (
  SpsVamProductDescriptionMappingService,
)
from app.services.tag_value_mapping.tag_value_mapping import TagValueMapping
from app.services.tag_value_mapping.wil.wil_product_mapping_service import (
  WilProductMappingService,
)
from app.services.tag_value_mapping.win.win_production_line_status_mapping_service import (
  WinProductionLineStatusMappingService,
)
from app.services.tag_value_mapping.win.win_production_status_mapping_service import (
  WinProductionStatusMappingService,
)


class TagValueMappingFactory:
    def __init__(self) -> None:
        self._mapper = {
            "RLN-SHYVECFBR-ProductDescription": ShyProductDescriptionMappingService(),
            "RLN-NARFLKU01-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU02-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU03-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU04-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU05-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU06-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU07-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU08-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU09-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU10-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU11-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU12-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU13-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU14-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU15-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU16-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU17-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU18-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU19-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU20-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU21-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU22-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU23-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU24-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU25-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-NARFLKU26-ProductDescription": NarProductDescriptionMappingService(),
            "RLN-BISCMP517-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMP518-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMPN92-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMPO92-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMPLN2-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMPLN3-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMP133-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCMPL70-ProductionLineStatus": BisProductionLineStatusMappingService(),
            "RLN-BISCHMPFM-ProductionLineStatus1": BisProductionLineStatusMappingService(),
            "RLN-BISCHMPFM-ProductionLineStatus2": BisProductionLineStatusMappingService(),
            "RLN-BISCHMPFM-ProductDescription": BisChemicalsPRFMProductDescriptionMappingService(),
            "RLN-BISCHMMO3-ProductionLineStatus1": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO3-ProductionLineStatus2": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO3-ProductDescription": BisChemicalsMO3ProductDescriptionMappingService(),
            "RLN-BISCHMMO4-ProductionLineStatus1": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO4-ProductionLineStatus2": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO4-ProductionLineStatus3": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO4-ProductionLineStatus4": BisProductionLineStatusMappingService(),
            "RLN-BISCHMMO4-ProductDescription": BisChemicalsMO4ProductDescriptionMappingService(),
            "RLN-NANCMPLN3-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN4-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN5-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN6-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN9-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN7-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-CLKMS1MS1-ProductDescription": CLKMS1ProductDescriptionMappingService(),
            "RLN-CLKVAMVAM-ProductDescription": CLKVAMProductDescriptionMappingService(),
            "RLN-CLKAANAAN-ProductDescription": CLKAAProductDescriptionMappingService(),
            "RLN-CLKAASAAS-ProductDescription": CLKAAProductDescriptionMappingService(),
            "RLN-FRAEUSPLY-ProductDescription": FraEusPlyProductDescriptionMappingService(),
            # STS-EVA
            "RLN-EVACMPL10-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL11-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL12-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL13-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL14-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL15-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL16-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL17-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-EVACMPL20-ProductionLineStatus": EvaProductionLineStatusMappingService(),
            "RLN-FRAVAMVAM-ProductDescription": FraVamVamProductDescriptionMappingService(),
            "RLN-BAYVAMVAM-ProductDescription": BAYVAMProductDescriptionMappingService(),
            "RLN-FRAVAMACH-ProductDescription": FraVamAchProductDescriptionMappingService(),
            "RLN-FRAEUSCRH-ProductDescription": FraEusCRHProductDescriptionMappingService(),
            "RLN-FRAEUSDBM-ProductDescription": FraEusDBMProductDescriptionMappingService(),
            "RLN-FRAEUS3G8-ProductDescription": FraEus3G8ProductDescriptionMappingService(),
            "RLN-OBHGURGUR-Product": ObhProductMappingService(),

            "RLN-FRACHMPM1-ProductDescription": FracPomProductDescriptionMappingService(),
            "RLN-FRACHMPM2-ProductDescription": FracPomProductDescriptionMappingService(),
            "RLN-FRACHMPM1-Product": FracPomProductDescriptionMappingService(),
            "RLN-FRACHMPM2-Product": FracPomProductDescriptionMappingService(),

            # STS-NAN
            "RLN-NANCELLN1-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCELLN2-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-NANCMPLN2-ProductionLineStatus": NanProductionLineStatusMappingService(),
            "RLN-FLOCMP1001-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP1101-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP1501-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP1701-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP1901-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP2001-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP2101-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP2201-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP2301-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP4001-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP4003-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP4004-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP4005-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP4006-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP7001-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP7002-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP7003-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP7004-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-FLOCMP701-ProductionLineStatus": FloProductionLineStatusMappingService(),
            "RLN-NANVAMVAM-ProductDescription": NanVAMProductDescriptionMappingService(),
            "RLN-WILFORLN2-Product": WilProductMappingService(),
            "RLN-WILFORLN1-Product": WilProductMappingService(),
            "RLN-BISGURGUR-Product": BisGurProductMappingService(),
            "RLN-NANHACHAC-ProductDescription": NanHacProductDescriptionMappingService(),
            "RLN-NANGURGUR-ProductDescription": NanGurProductMappingService(),
            "RLN-NANGURGUR-Product": NanGurProductMappingService(),
            # STS-WIN
            "RLN-WINLFTL01-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL01-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINLFTL03-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL03-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINLFTL04-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL04-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINCFRL05-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINCFRL05-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINLFTL10-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL10-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINLFTL11-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL11-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-WINLFTL12-ProductTrial": WinProductionStatusMappingService(),
            "RLN-WINLFTL12-ProductionLineStatus": WinProductionLineStatusMappingService(),
            "RLN-SPSESTEST-Product": SpsEstProductMappingService(),
            "RLN-SPSESTEST-ProductDescription": SpsEstProductDescriptionMappingService(),
            # SPS - VAM
            "RLN-SPSVAMVAM-ProductDescription": SpsVamProductDescriptionMappingService(),
            "RLN-SPSHACHAC-ProductDescription": SpsHacProductDescriptionMappingService(),
            # SPS - CAN
            "RLN-CANMEAMEA-ProductDescription": CanMeaProductDescriptionMappingService(),
            "RLN-CANMEOMEO-ProductDescription": CanMeoProductDescriptionMappingService(),
            "RLN-CANANHANH-ProductDescription": CanAc20ProductDescriptionMappingService(),
            "RLN-CANACEACE-ProductDescription": CanMibkProductDescriptionMappingService(),
            "RLN-CANETAETA-ProductDescription": CanETAProductDescriptionMappingService(),
            # FRA - COMPOUNDING
            "RLN-FRACMP511-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP512-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP513-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP521-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP522-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP523-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP524-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP525-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP526-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP527-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP528-ProductionLineStatus": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP511-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP512-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP513-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP521-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP522-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP523-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP524-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP525-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP526-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP527-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP528-ProductionLineStatus_2": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP511-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP512-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP513-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP521-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP522-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP523-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP524-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP525-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP526-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP527-StartingUp": FraCompondingProductionLineStatusMappingService(),
            "RLN-FRACMP528-StartingUp": FraCompondingProductionLineStatusMappingService(),
            # NPT - COMPOUNDING
            "RLN-NPTSPRN01-ProductionLineStatus": NptProductionLineStatusMappingService(),
            "RLN-NPTSPRWW3-ProductionLineStatus": NptProductionLineStatusMappingService(),
            "RLN-NPTSPRWW6-ProductionLineStatus": NptProductionLineStatusMappingService(),
            "RLN-NPTSPRWW7-ProductionLineStatus": NptProductionLineStatusMappingService(),
            "RLN-NPTSPRWW9-ProductionLineStatus": NptProductionLineStatusMappingService(),
            "RLN-NPTSPRW10-ProductionLineStatus": NptProductionLineStatusMappingService(),
            # GEL - BATCH
            "RLN-GELVAE6K2-ProductionLineStatus": GelVaeProductionLineDescriptionMappingService(),
            "RLN-GELVAE6K3-ProductionLineStatus": GelVaeProductionLineDescriptionMappingService(),
            "RLN-GELVAE6K4-ProductionLineStatus": GelVaeProductionLineDescriptionMappingService(),
        }

    def create(self, reporting_line: str, tag_alias: str) -> TagValueMapping:
        return self._mapper[f"{reporting_line}-{tag_alias}"]
