from typing import Optional

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.event_identification.bay.bayvam_event_identification_service import (
  BAYVAMEventIdentificationService,
)
from app.services.event_identification.bch.bch_batch_event_identification_service import (
  BchBatchEventIdentificationService,
)
from app.services.event_identification.bis.bis_compounding_1_event_identification_service import (
  BisCompoundingFirstEventIdentificationService,
)
from app.services.event_identification.bis.bis_compounding_2_event_identification_service import (
  BisCompoundingSecondEventIdentificationService,
)
from app.services.event_identification.bis.bis_compounding_3_event_identification_service import (
  BisCompoundingThirdEventIdentificationService,
)
from app.services.event_identification.bis.bis_continuous_event_identification_service import (
  BisContinuousEventIdentificationService,
)
from app.services.event_identification.bis.bis_continuous_MO3_event_identification_service import (
  BisMO3EventIdentificationService,
)
from app.services.event_identification.bis.bis_continuous_MO4_event_identification_service import (
  BisMO4EventIdentificationService,
)
from app.services.event_identification.bis.bis_continuous_PARAFORM_event_identification_service import (
  BisPARAFORMEventIdentificationService,
)
from app.services.event_identification.bis.bis_gur_continuos_event_identification_service import (
  BisGurContinuosEventIdentificationService,
)
from app.services.event_identification.can.can_ac20_event_identification_service import (
  CanAc20EventIdentificationService,
)
from app.services.event_identification.can.can_eta_event_identification_service import (
  CanETAEventIdentificationService,
)
from app.services.event_identification.can.can_mea_event_identification_service import (
  CanMeaEventIdentificationService,
)
from app.services.event_identification.can.can_meo_event_identification_service import (
  CanMeoEventIdentificationService,
)
from app.services.event_identification.can.can_mibk_event_identification_service import (
  CanMibkEventIdentificationService,
)
from app.services.event_identification.clk.clkaan_event_identification_service import (
  CLKAANEventIdentificationService,
)
from app.services.event_identification.clk.clkaas_event_identification_service import (
  CLKAASEventIdentificationService,
)
from app.services.event_identification.clk.clkms1_event_identification_service import (
  CLKMS1EventIdentificationService,
)
from app.services.event_identification.clk.clkvam_event_identification_service import (
  CLKVAMEventIdentificationService,
)
from app.services.event_identification.eda.eda_continuos_event_identification_service import (
  EdaEventIdentificationService,
)
from app.services.event_identification.eno.continuous_event_identification_service import (
  ContinuousEventIdentificationService,
)
from app.services.event_identification.eno.default_batch_event_identification_service import (
  DefaultBatchEventIdentificationService,
)
from app.services.event_identification.eno.vae_event_identification_service import (
  VAEEventIdentificationService,
)
from app.services.event_identification.eva.eva_event_identification_service import (
  EvaCompoundingEventIdentificationService,
)
from app.services.event_identification.event_identification import EventIdentification
from app.services.event_identification.flo.flo_compounding_event_identification_service import (
  FloCompoundingEventIdentificationService,
)
from app.services.event_identification.forly.for_event_identification_service import (
  ForCompoundingEventIdentificationService,
)
from app.services.event_identification.forly.for_par_d1d2_event_identification_service import (
  ForParCompoundingEventIdentificationService,
)
from app.services.event_identification.frac.frac_3g8_event_identification_service import (
  Frac3g8EventIdentificationService,
)
from app.services.event_identification.frac.frac_ach_event_identification_service import (
  FracACHEventIdentificationService,
)
from app.services.event_identification.frac.frac_batch_event_identification_service import (
  FracBatchEventIdentificationService,
)
from app.services.event_identification.frac.frac_compounding_event_identification_service import (
  FraCompoundingFirstEventIdentificationService,
)
from app.services.event_identification.frac.frac_croton_event_identification_service import (
  FracCrotonEventIdentificationService,
)
from app.services.event_identification.frac.frac_dbm_event_identification_service import (
  FracDBMEventIdentificationService,
)
from app.services.event_identification.frac.frac_polyo_event_identification_service import (
  FracPolyoEventIdentificationService,
)
from app.services.event_identification.frac.frac_pom_event_identification_service import (
  FracPomEventIdentificationService,
)
from app.services.event_identification.frac.frac_vam_event_identification_service import (
  FracVAMEventIdentificationService,
)
from app.services.event_identification.gel.gel_batch_event_identification_service import (
  GELBatchEventIdentificationService,
)
from app.services.event_identification.nan.nan_anhydride_acetic_event_identification_service import (
  NanAnhydrideAceticEventIdentificationService,
)
from app.services.event_identification.nan.nan_batch_event_identification_service import (
  NanBatchEventIdentificationService,
)
from app.services.event_identification.nan.nan_compounding_event_identification_service import (
  NanCompoundingEventIdentificationService,
)
from app.services.event_identification.nan.nan_gur_event_identification_service import (
  NanGurEventIdentificationService,
)
from app.services.event_identification.nan.nan_hac_event_identification_service import (
  NanHacEventIdentificationService,
)
from app.services.event_identification.nan.nan_ltf_event_identification_service import (
  NanLTFEventIdentificationService,
)
from app.services.event_identification.nan.nan_vam_event_identification_service import (
  NanVAMEventIdentificationService,
)
from app.services.event_identification.nar.default_batch_event_identification_service import (
  NarBatchEventIdentificationService,
)
from app.services.event_identification.nar.nar_anhydride_continuous_event_identification_service import (
  NarAnhydrideContinuousEventIdentificationService,
)
from app.services.event_identification.nar.nar_continuous_event_identification_service import (
  NarContinuousEventIdentificationService,
)
from app.services.event_identification.npt.npt_compounding_event_identification_service import (
  NptCompoundingEventIdentificationService,
)
from app.services.event_identification.pen.pen_compounding_event_identification_service import (
  PenCompoundingEventIdentificationService,
)
from app.services.event_identification.per.per_batch_event_identification_service import (
  PerBatchEventIdentificationService,
)
from app.services.event_identification.she.she_rdp_event_identification_service import (
  SheRdpEventIdentificationService,
)
from app.services.event_identification.shy.shy_batch_event_identification_service import (
  ShyBatchEventIdentificationService,
)
from app.services.event_identification.shy.shy_batch_FBR_line_event_identification_service import (
  ShyBatchFBRLineEventIdentificationService,
)
from app.services.event_identification.shy.shy_batch_SSP_line_event_identification_service import (
  ShyBatchSSPLineEventIdentificationService,
)
from app.services.event_identification.sps.sps_batch_event_identification_service import (
  SpsBatchEventIdentificationService,
)
from app.services.event_identification.sps.sps_est_event_identification_service import (
  SpsEstEventIdentificationService,
)
from app.services.event_identification.sps.sps_hac_event_identification_service import (
  SpsHacEventIdentificationService,
)
from app.services.event_identification.sps.sps_vam_event_identification_service import (
  SpsVamEventIdentificationService,
)
from app.services.event_identification.utz.utz_compounding_event_identification_service import (
  UtzCompoundingEventIdentificationService,
)
from app.services.event_identification.win.win_compounding_event_identification_service import (
  WinCompoundingFirstEventIdentificationService,
)

from .obh.obh_continuous_event_identification_service import (
  ObhContinuousEventIdentificationService,
)

from .was.was_fill_event_identification_service import (
    WASFILEventIdentificationService,
)

from .wil.wil_continuous_event_identification_service import (
  WilContinuousEventIdentificationService,
)


class EventIdentificationFactory:
    def __init__(
        self,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: Optional[pd.DataFrame],
        mdr: Optional[pd.DataFrame],
        bbct: Optional[pd.DataFrame],
    ) -> None:
        self._msdp = msdp
        self._mdr = mdr
        self._bbct = bbct
        self._mapper = {
            "RLN-ENRCNTCNT": ContinuousEventIdentificationService(
                "RLN-ENRCNTCNT", reporting_site_configuration
            ),  # COntinuous
            "RLN-ENRCNVR07": DefaultBatchEventIdentificationService(
                "RLN-ENRCNVR07", reporting_site_configuration
            ),  # R7
            "RLN-ENRCNVR10": DefaultBatchEventIdentificationService(
                "RLN-ENRCNVR10", reporting_site_configuration
            ),  # R10
            "RLN-ENRCNVR12": DefaultBatchEventIdentificationService(
                "RLN-ENRCNVR12", reporting_site_configuration
            ),  # R12
            "RLN-ENRVAEE01": VAEEventIdentificationService(
                "RLN-ENRVAEE01", reporting_site_configuration
            ),  # VAE1
            "RLN-ENRVAEE02": VAEEventIdentificationService(
                "RLN-ENRVAEE02", reporting_site_configuration
            ),  # VAE2
            "RLN-PERCNV202": PerBatchEventIdentificationService(
                "RLN-PERCNV202", reporting_site_configuration
            ),  # R202
            "RLN-PERCNV302": PerBatchEventIdentificationService(
                "RLN-PERCNV302", reporting_site_configuration
            ),  # R302
            "RLN-PERCNV602": PerBatchEventIdentificationService(
                "RLN-PERCNV602", reporting_site_configuration
            ),  # R602
            "RLN-PERVAE125": PerBatchEventIdentificationService(
                "RLN-PERVAE125", reporting_site_configuration
            ),  # R1250
            "RLN-PERVAE225": PerBatchEventIdentificationService(
                "RLN-PERVAE225", reporting_site_configuration
            ),  # R2250
            "RLN-PERVAE325": PerBatchEventIdentificationService(
                "RLN-PERVAE325", reporting_site_configuration
            ),  # R3250
            "RLN-NARFLKD09": NarContinuousEventIdentificationService(
                "RLN-NARFLKD09",
                "UNT-NARFLK",
                reporting_site_configuration,
                self._msdp,
            ),  # CONT
            "RLN-SHYVECL16": ShyBatchEventIdentificationService(
                "RLN-SHYVECL16", reporting_site_configuration
            ),  # L16
            "RLN-SHYVECL17": ShyBatchEventIdentificationService(
                "RLN-SHYVECL17", reporting_site_configuration
            ),  # L17
            "RLN-SHYVECSSP": ShyBatchSSPLineEventIdentificationService(
                "RLN-SHYVECSSP", reporting_site_configuration
            ),  # SSP
            "RLN-SHYVECFBR": ShyBatchFBRLineEventIdentificationService(
                "RLN-SHYVECFBR", reporting_site_configuration
            ),  # FBR
            "RLN-FRACNV101": FracBatchEventIdentificationService(
                "RLN-FRACNV101", reporting_site_configuration, self._bbct
            ),  # 101
            "RLN-FRACNV102": FracBatchEventIdentificationService(
                "RLN-FRACNV102", reporting_site_configuration, self._bbct
            ),  # 102
            "RLN-FRACNV103": FracBatchEventIdentificationService(
                "RLN-FRACNV103", reporting_site_configuration, self._bbct
            ),  # 103
            "RLN-FRACNV105": FracBatchEventIdentificationService(
                "RLN-FRACNV105", reporting_site_configuration, self._bbct
            ),  # 105
            "RLN-FRACNV106": FracBatchEventIdentificationService(
                "RLN-FRACNV106", reporting_site_configuration, self._bbct
            ),  # 106
            "RLN-FRACNV107": FracBatchEventIdentificationService(
                "RLN-FRACNV107", reporting_site_configuration, self._bbct
            ),  # 107
            "RLN-FRACNV108": FracBatchEventIdentificationService(
                "RLN-FRACNV108", reporting_site_configuration, self._bbct
            ),  # 108
            "RLN-FRACNV109": FracBatchEventIdentificationService(
                "RLN-FRACNV109", reporting_site_configuration, self._bbct
            ),  # 109
            "RLN-FRACNV112": FracBatchEventIdentificationService(
                "RLN-FRACNV112", reporting_site_configuration, self._bbct
            ),  # 112
            "RLN-FRACNV113": FracBatchEventIdentificationService(
                "RLN-FRACNV113", reporting_site_configuration, self._bbct
            ),  # 113
            "RLN-FRACNV118": FracBatchEventIdentificationService(
                "RLN-FRACNV118", reporting_site_configuration, self._bbct
            ),  # 118
            "RLN-FRACNV119": FracBatchEventIdentificationService(
                "RLN-FRACNV119", reporting_site_configuration, self._bbct
            ),  # 119
            "RLN-FRACNV121": FracBatchEventIdentificationService(
                "RLN-FRACNV121", reporting_site_configuration, self._bbct
            ),  # 121
            "RLN-FRACNV123": FracBatchEventIdentificationService(
                "RLN-FRACNV123", reporting_site_configuration, self._bbct
            ),  # 123
            "RLN-FRACNV125": FracBatchEventIdentificationService(
                "RLN-FRACNV125", reporting_site_configuration, self._bbct
            ),  # 125
            "RLN-FRACNV128": FracBatchEventIdentificationService(
                "RLN-FRACNV128", reporting_site_configuration, self._bbct
            ),  # 128
            "RLN-FRAVAERX1": FracBatchEventIdentificationService(
                "RLN-FRAVAERX1", reporting_site_configuration, self._bbct
            ),  # R1
            "RLN-FRAVAERX2": FracBatchEventIdentificationService(
                "RLN-FRAVAERX2", reporting_site_configuration, self._bbct
            ),  # R2
            "RLN-FRAVAERX3": FracBatchEventIdentificationService(
                "RLN-FRAVAERX3", reporting_site_configuration, self._bbct
            ),  # R3
            "RLN-FRAVAERX4": FracBatchEventIdentificationService(
                "RLN-FRAVAERX4", reporting_site_configuration, self._bbct
            ),  # R4
            "RLN-FRAVAERX5": FracBatchEventIdentificationService(
                "RLN-FRAVAERX5", reporting_site_configuration, self._bbct
            ),  # R5
            "RLN-FRAVAERX6": FracBatchEventIdentificationService(
                "RLN-FRAVAERX6", reporting_site_configuration, self._bbct
            ),  # R6
            "RLN-NARCRUKE1": NarAnhydrideContinuousEventIdentificationService(
                "RLN-NARCRUKE1",
                "UNT-NARCRU",
                reporting_site_configuration,
                self._msdp,
            ),  # CONT
            "RLN-NARCRUKE2": NarAnhydrideContinuousEventIdentificationService(
                "RLN-NARCRUKE2",
                "UNT-NARCRU",
                reporting_site_configuration,
                self._msdp,
            ),  # CONT
            "RLN-BISCMPLN2": BisCompoundingFirstEventIdentificationService(
                "RLN-BISCMPLN2", reporting_site_configuration, self._mdr
            ),  # Line 2
            "RLN-BISCMPLN3": BisCompoundingFirstEventIdentificationService(
                "RLN-BISCMPLN3", reporting_site_configuration, self._mdr
            ),  # Line 3
            "RLN-BISCMPN92": BisCompoundingSecondEventIdentificationService(
                "RLN-BISCMPN92", reporting_site_configuration, self._mdr
            ),  # N92
            "RLN-BISCMPO92": BisCompoundingSecondEventIdentificationService(
                "RLN-BISCMPO92", reporting_site_configuration, self._mdr
            ),  # O92
            "RLN-BISCMPL70": BisCompoundingSecondEventIdentificationService(
                "RLN-BISCMPL70", reporting_site_configuration, self._mdr
            ),  # Line 70
            "RLN-BISCMP133": BisCompoundingSecondEventIdentificationService(
                "RLN-BISCMP133", reporting_site_configuration, self._mdr
            ),  # Line 133
            "RLN-BISCMP517": BisCompoundingThirdEventIdentificationService(
                "RLN-BISCMP517", reporting_site_configuration, self._mdr
            ),  # Line 517
            "RLN-BISCMP518": BisCompoundingThirdEventIdentificationService(
                "RLN-BISCMP518", reporting_site_configuration, self._mdr
            ),  # Line 518
            "RLN-BISGURGUR": BisGurContinuosEventIdentificationService(
                "RLN-BISGURGUR",
                "UNT-BISGUR",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-BISCHMPFM": BisPARAFORMEventIdentificationService(
                "RLN-BISCHMPFM",
                "UNT-BISCHM",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-BISCHMMO3": BisMO3EventIdentificationService(
                "RLN-BISCHMMO3",
                "UNT-BISCHM",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-BISCHMMO4": BisMO4EventIdentificationService(
                "RLN-BISCHMMO4",
                "UNT-BISCHM",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-NARFLKU01": NarBatchEventIdentificationService(
                "RLN-NARFLKU01", reporting_site_configuration
            ),  # UNIT01
            "RLN-NARFLKU02": NarBatchEventIdentificationService(
                "RLN-NARFLKU02", reporting_site_configuration
            ),  # UNIT02
            "RLN-NARFLKU03": NarBatchEventIdentificationService(
                "RLN-NARFLKU03", reporting_site_configuration
            ),  # UNIT03
            "RLN-NARFLKU04": NarBatchEventIdentificationService(
                "RLN-NARFLKU04", reporting_site_configuration
            ),  # UNIT04
            "RLN-NARFLKU05": NarBatchEventIdentificationService(
                "RLN-NARFLKU05", reporting_site_configuration
            ),  # UNIT05
            "RLN-NARFLKU06": NarBatchEventIdentificationService(
                "RLN-NARFLKU06", reporting_site_configuration
            ),  # UNIT06
            "RLN-NARFLKU07": NarBatchEventIdentificationService(
                "RLN-NARFLKU07", reporting_site_configuration
            ),  # UNIT07
            "RLN-NARFLKU08": NarBatchEventIdentificationService(
                "RLN-NARFLKU08", reporting_site_configuration
            ),  # UNIT08
            "RLN-NARFLKU09": NarBatchEventIdentificationService(
                "RLN-NARFLKU09", reporting_site_configuration
            ),  # UNIT09
            "RLN-NARFLKU10": NarBatchEventIdentificationService(
                "RLN-NARFLKU10", reporting_site_configuration
            ),  # UNIT10
            "RLN-NARFLKU11": NarBatchEventIdentificationService(
                "RLN-NARFLKU11", reporting_site_configuration
            ),  # UNIT11
            "RLN-NARFLKU12": NarBatchEventIdentificationService(
                "RLN-NARFLKU12", reporting_site_configuration
            ),  # UNIT12
            "RLN-NARFLKU13": NarBatchEventIdentificationService(
                "RLN-NARFLKU13", reporting_site_configuration
            ),  # UNIT13
            "RLN-NARFLKU14": NarBatchEventIdentificationService(
                "RLN-NARFLKU14", reporting_site_configuration
            ),  # UNIT14
            "RLN-NARFLKU15": NarBatchEventIdentificationService(
                "RLN-NARFLKU15", reporting_site_configuration
            ),  # UNIT15
            "RLN-NARFLKU16": NarBatchEventIdentificationService(
                "RLN-NARFLKU16", reporting_site_configuration
            ),  # UNIT16
            "RLN-NARFLKU17": NarBatchEventIdentificationService(
                "RLN-NARFLKU17", reporting_site_configuration
            ),  # UNIT17
            "RLN-NARFLKU18": NarBatchEventIdentificationService(
                "RLN-NARFLKU18", reporting_site_configuration
            ),  # UNIT18
            "RLN-NARFLKU19": NarBatchEventIdentificationService(
                "RLN-NARFLKU19", reporting_site_configuration
            ),  # UNIT19
            "RLN-NARFLKU20": NarBatchEventIdentificationService(
                "RLN-NARFLKU20", reporting_site_configuration
            ),  # UNIT20
            "RLN-NARFLKU21": NarBatchEventIdentificationService(
                "RLN-NARFLKU21", reporting_site_configuration
            ),  # UNIT21
            "RLN-NARFLKU22": NarBatchEventIdentificationService(
                "RLN-NARFLKU22", reporting_site_configuration
            ),  # UNIT22
            "RLN-NARFLKU23": NarBatchEventIdentificationService(
                "RLN-NARFLKU23", reporting_site_configuration
            ),  # UNIT23
            "RLN-NARFLKU24": NarBatchEventIdentificationService(
                "RLN-NARFLKU24", reporting_site_configuration
            ),  # UNIT24
            "RLN-NARFLKU25": NarBatchEventIdentificationService(
                "RLN-NARFLKU25", reporting_site_configuration
            ),  # UNIT25
            "RLN-NARFLKU26": NarBatchEventIdentificationService(
                "RLN-NARFLKU26", reporting_site_configuration
            ),  # UNIT26
            "RLN-CLKMS1MS1": CLKMS1EventIdentificationService(
                "RLN-CLKMS1MS1",
                "UNT-CLKMS1",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-CLKAANAAN": CLKAANEventIdentificationService(
                "RLN-CLKAANAAN",
                "UNT-CLKAAN",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-CLKAASAAS": CLKAASEventIdentificationService(
                "RLN-CLKAASAAS",
                "UNT-CLKAAS",
                reporting_site_configuration,
                self._msdp,
            ),
            "RLN-CLKVAMVAM": CLKVAMEventIdentificationService(
                "RLN-CLKVAMVAM",
                "UNT-CLKVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # CLK-VAM COntinuous
            "RLN-NANANHANH": NanAnhydrideAceticEventIdentificationService(
                "RLN-NANANHANH",
                "UNT-NANANH",
                reporting_site_configuration,
                self._msdp,
            ),  # ANH
            "RLN-NANVAE801": NanBatchEventIdentificationService(
                "RLN-NANVAE801", reporting_site_configuration, self._bbct
            ),  # 801
            "RLN-NANVAE280": NanBatchEventIdentificationService(
                "RLN-NANVAE280", reporting_site_configuration, self._bbct
            ),  # 280
            "RLN-NANVAE821": NanBatchEventIdentificationService(
                "RLN-NANVAE821", reporting_site_configuration, self._bbct
            ),  # 821
            "RLN-NANVAE580": NanBatchEventIdentificationService(
                "RLN-NANVAE580", reporting_site_configuration, self._bbct
            ),  # 580
            "RLN-NANCMPLN3": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN3", reporting_site_configuration
            ),  # 40MM3
            "RLN-NANCMPLN4": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN4", reporting_site_configuration
            ),  # 40MM4
            "RLN-NANCMPLN5": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN5", reporting_site_configuration
            ),  # 70MM5
            "RLN-NANCMPLN6": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN6", reporting_site_configuration
            ),  # 70MM6
            "RLN-NANCMPLN7": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN7", reporting_site_configuration
            ),  # 92MM7
            "RLN-NANCMPLN9": NanCompoundingEventIdentificationService(
                "RLN-NANCMPLN9", reporting_site_configuration
            ),  # 70MM9
            "RLN-BCHCNVRX1": BchBatchEventIdentificationService(
                "RLN-BCHCNVRX1",
                reporting_site_configuration,
                # RX1
            ),
            "RLN-BCHCNVRX2": BchBatchEventIdentificationService(
                "RLN-BCHCNVRX2",
                reporting_site_configuration,
                # RX2
            ),
            "RLN-BCHCNVRX3": BchBatchEventIdentificationService(
                "RLN-BCHCNVRX3",
                reporting_site_configuration,
                # RX3
            ),
            "RLN-FRAEUSPLY": FracPolyoEventIdentificationService(
                "RLN-FRAEUSPLY",
                "UNT-FRAEUS",
                reporting_site_configuration,
                self._msdp,
            ),  # Poly O
            "RLN-FRACHMPM1": FracPomEventIdentificationService(
                "RLN-FRACHMPM1",
                "UNT-FRAPOL",
                reporting_site_configuration,
                self._msdp,
            ),  # Pom 1

            "RLN-FRACHMPM2": FracPomEventIdentificationService(
                "RLN-FRACHMPM2",
                "UNT-FRAPOL",
                reporting_site_configuration,
                self._msdp,
            ),  # Pom 2
            "RLN-FRAEUSCRH": FracCrotonEventIdentificationService(
                "RLN-FRAEUSCRH",
                "UNT-FRAEUS",
                reporting_site_configuration,
                self._msdp,
            ),  # Croton
            "RLN-SPSESTEST": SpsEstEventIdentificationService(
                "RLN-SPSESTEST",
                "UNT-SPSEST",
                reporting_site_configuration,
                self._msdp,
            ),  # Esters
            "RLN-FRAVAMVAM": FracVAMEventIdentificationService(
                "RLN-FRAVAMVAM",
                "UNT-FRAVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # VAM
            "RLN-FRAVAMACH": FracACHEventIdentificationService(
                "RLN-FRAVAMACH",
                "UNT-FRAVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # ACH
            # "RLN-EDAPOL3RX": EdaEventIdentificationService(
            #     "RLN-EDAPOL3RX",
            #     reporting_site_configuration,
            #     # EDAPOL3RX
            # ),
            # "RLN-EDAPOL4RX": EdaEventIdentificationService(
            #     "RLN-EDAPOL4RX",
            #     reporting_site_configuration,
            #     # EDAPOL4RX
            # ),
            # "RLN-EDAPOL5RX": EdaEventIdentificationService(
            #     "RLN-EDAPOL5RX",
            #     reporting_site_configuration,
            #     # EDAPOL5RX
            # ),
            "RLN-BAYVAMVAM": BAYVAMEventIdentificationService(
                "RLN-BAYVAMVAM",
                "UNT-BAYVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # BAYVAMVAM
            # STS-EVA
            "RLN-EVACMPL10": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL10", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL10
            "RLN-EVACMPL11": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL11", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL11
            "RLN-EVACMPL12": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL12", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL12
            "RLN-EVACMPL13": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL13", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL13
            "RLN-EVACMPL14": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL14", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL14
            "RLN-EVACMPL15": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL15", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL15
            "RLN-EVACMPL16": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL16", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL16
            "RLN-EVACMPL17": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL17", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL17
            "RLN-EVACMPL20": EvaCompoundingEventIdentificationService(
                "RLN-EVACMPL20", reporting_site_configuration, self._mdr
            ),  # RLN-EVACMPL20
            "RLN-SPSVAEVAE": SpsBatchEventIdentificationService(
                "RLN-SPSVAEVAE", reporting_site_configuration, self._bbct
            ),
            "RLN-FRAEUSDBM": FracDBMEventIdentificationService(
                "RLN-FRAEUSDBM",
                "UNT-FRAEUS",
                reporting_site_configuration,
                self._msdp,
            ),  # DBM
            "RLN-OBHGURGUR": ObhContinuousEventIdentificationService(
                "RLN-OBHGURGUR",
                "UNT-OBHGUR",
                reporting_site_configuration,
                self._msdp,
            ),  # GUR
            "RLN-FRAEUS3G8": Frac3g8EventIdentificationService(
                "RLN-FRAEUS3G8",
                "UNT-FRAEUS",
                reporting_site_configuration,
                self._msdp,
            ),  # 3G8
            # STS-NAN - LTG
            "RLN-NANCELLN1": NanLTFEventIdentificationService(
                "RLN-NANCELLN1", reporting_site_configuration, self._mdr
            ),
            "RLN-NANCELLN2": NanLTFEventIdentificationService(
                "RLN-NANCELLN2", reporting_site_configuration, self._mdr
            ),
            "RLN-WILFORLN1": WilContinuousEventIdentificationService(
                "RLN-WILFORLN1",
                "UNT-WILFOR",
                reporting_site_configuration,
                self._msdp,
            ),  # WIL
            "RLN-WILFORLN2": WilContinuousEventIdentificationService(
                "RLN-WILFORLN2",
                "UNT-WILFOR",
                reporting_site_configuration,
                self._msdp,
            ),  # WIL
            "RLN-FLOCMP1001": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP1001", reporting_site_configuration, self._mdr
            ),  # Line 1001
            "RLN-FLOCMP1101": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP1101", reporting_site_configuration, self._mdr
            ),  # Line 1101
            "RLN-FLOCMP1501": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP1501", reporting_site_configuration, self._mdr
            ),  # Line 1501
            "RLN-FLOCMP1701": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP1701", reporting_site_configuration, self._mdr
            ),  # Line 1701
            "RLN-FLOCMP1901": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP1901", reporting_site_configuration, self._mdr
            ),  # Line 1901
            "RLN-FLOCMP2001": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP2001", reporting_site_configuration, self._mdr
            ),  # Line 2001
            "RLN-FLOCMP2101": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP2101", reporting_site_configuration, self._mdr
            ),  # Line 2101
            "RLN-FLOCMP2201": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP2201", reporting_site_configuration, self._mdr
            ),  # Line 2201
            "RLN-FLOCMP2301": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP2301", reporting_site_configuration, self._mdr
            ),  # Line 2301
            "RLN-FLOCMP4001": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP4001", reporting_site_configuration, self._mdr
            ),  # Line 4001
            "RLN-FLOCMP4003": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP4003", reporting_site_configuration, self._mdr
            ),  # Line 4003
            "RLN-FLOCMP4004": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP4004", reporting_site_configuration, self._mdr
            ),  # Line 4004
            "RLN-FLOCMP4005": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP4005", reporting_site_configuration, self._mdr
            ),  # Line 4005
            "RLN-FLOCMP4006": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP4006", reporting_site_configuration, self._mdr
            ),  # Line 4006
            "RLN-FLOCMP7001": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP7001", reporting_site_configuration, self._mdr
            ),  # Line 7001
            "RLN-FLOCMP7002": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP7002", reporting_site_configuration, self._mdr
            ),  # Line 7002
            "RLN-FLOCMP7003": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP7003", reporting_site_configuration, self._mdr
            ),  # Line 7003
            "RLN-FLOCMP7004": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP7004", reporting_site_configuration, self._mdr
            ),  # Line 7004
            "RLN-FLOCMP701": FloCompoundingEventIdentificationService(
                "RLN-FLOCMP701", reporting_site_configuration, self._mdr
            ),  # Line 701
            "RLN-NANVAMVAM": NanVAMEventIdentificationService(
                "RLN-NANVAMVAM",
                "UNT-NANVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # VAM
            "RLN-BISPOMSHY": BisContinuousEventIdentificationService(
                "RLN-BISPOMSHY",
                "UNT-BISPOM",
                reporting_site_configuration,
                self._msdp,
            ),  # POM
            "RLN-FORETPQ02": ForCompoundingEventIdentificationService(
                "RLN-FORETPQ02", reporting_site_configuration, self._mdr
            ),  # RLN-FORETPQ02
            "RLN-FORETPQ04": ForCompoundingEventIdentificationService(
                "RLN-FORETPQ04", reporting_site_configuration, self._mdr
            ),  # RLN-FORETPQ04
            "RLN-FORETPQ05": ForCompoundingEventIdentificationService(
                "RLN-FORETPQ05", reporting_site_configuration, self._mdr
            ),  # RLN-FORETPQ05
            "RLN-FORETPQ06": ForCompoundingEventIdentificationService(
                "RLN-FORETPQ06", reporting_site_configuration, self._mdr
            ),  # RLN-FORETPQ06
            "RLN-NANHACHAC": NanHacEventIdentificationService(
                "RLN-NANHACHAC",
                "UNT-NANHAC",
                reporting_site_configuration,
                self._msdp,
            ),  # HAC
            # STS-WIN
            "RLN-WINLFTL01": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL01", reporting_site_configuration, self._mdr
            ),
            "RLN-WINLFTL03": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL03", reporting_site_configuration, self._mdr
            ),
            "RLN-WINLFTL04": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL04", reporting_site_configuration, self._mdr
            ),
            "RLN-WINCFRL05": WinCompoundingFirstEventIdentificationService(
                "RLN-WINCFRL05", reporting_site_configuration, self._mdr
            ),
            "RLN-WINLFTL10": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL10", reporting_site_configuration, self._mdr
            ),
            "RLN-WINLFTL11": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL11", reporting_site_configuration, self._mdr
            ),
            "RLN-WINLFTL12": WinCompoundingFirstEventIdentificationService(
                "RLN-WINLFTL12", reporting_site_configuration, self._mdr
            ),
            # STS-NAN (GUR)
            "RLN-NANGURGUR": NanGurEventIdentificationService(
                "RLN-NANGURGUR",
                "UNT-NANGUR",
                reporting_site_configuration,
                self._msdp,
            ),  # GUR
            "RLN-SPSVAMVAM": SpsVamEventIdentificationService(
                "RLN-SPSVAMVAM",
                "UNT-SPSVAM",
                reporting_site_configuration,
                self._msdp,
            ),  # STS-VAM
            "RLN-SPSHACHAC": SpsHacEventIdentificationService(
                "RLN-SPSHACHAC",
                "UNT-SPSHAC",
                reporting_site_configuration,
                self._msdp,
            ),  # HAC
            "RLN-CANMEAMEA": CanMeaEventIdentificationService(
                "RLN-CANMEAMEA",
                "UNT-CANMEA",
                reporting_site_configuration,
                self._msdp,
            ),  # Methyl Amines
            "RLN-CANMEOMEO": CanMeoEventIdentificationService(
                "RLN-CANMEOMEO",
                "UNT-CANMEO",
                reporting_site_configuration,
                self._msdp,
            ),  # Mesytil Oxide
            "RLN-CANANHANH": CanAc20EventIdentificationService(
                "RLN-CANANHANH",
                "UNT-CANANH",
                reporting_site_configuration,
                self._msdp,
            ),  # CAM Ac20
            "RLN-CANETAETA": CanETAEventIdentificationService(
                "RLN-CANETAETA",
                "UNT-CANETA",
                reporting_site_configuration,
                self._msdp,
            ),  # CAM EA
            "RLN-CANACEACE": CanMibkEventIdentificationService(
                "RLN-CANACEACE",
                "UNT-CANACE",
                reporting_site_configuration,
                self._msdp,
            ),  # CAM MIBK
            "RLN-FORLFTLF1": ForCompoundingEventIdentificationService(
                "RLN-FORLFTLF1", reporting_site_configuration, self._mdr
            ),  # RLN-FORLFTLF1
            "RLN-FORLFTLF4": ForCompoundingEventIdentificationService(
                "RLN-FORLFTLF4", reporting_site_configuration, self._mdr
            ),  # RLN-FORLFTLF4
            "RLN-FORLFTLF5": ForCompoundingEventIdentificationService(
                "RLN-FORLFTLF5", reporting_site_configuration, self._mdr
            ),  # RLN-FORLFTLF5
            "RLN-FORLFTLF2": ForCompoundingEventIdentificationService(
                "RLN-FORLFTLF2", reporting_site_configuration, self._mdr
            ),  # RLN-FORLFTLF2
            "RLN-FORLFTLF3": ForCompoundingEventIdentificationService(
                "RLN-FORLFTLF3", reporting_site_configuration, self._mdr
            ),  # RLN-FORLFTLF3
            "RLN-FORPA0Z03": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z03", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z03
            "RLN-FORPA0Z04": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z04", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z04
            "RLN-FORPA0Z05": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z05", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z05
            "RLN-FORPA0Z06": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z06", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z06
            "RLN-FORPA0Z07": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z07", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z07
            "RLN-FORPA0Z08": ForCompoundingEventIdentificationService(
                "RLN-FORPA0Z08", reporting_site_configuration, self._mdr
            ),  # RLN-FORPA0Z08
            "RLN-FORTPEA01": ForCompoundingEventIdentificationService(
                "RLN-FORTPEA01", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEA01
            "RLN-FORTPEA02": ForCompoundingEventIdentificationService(
                "RLN-FORTPEA02", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEA02
            "RLN-FORTPEA03": ForCompoundingEventIdentificationService(
                "RLN-FORTPEA03", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEA03
            "RLN-FORTPEA04": ForCompoundingEventIdentificationService(
                "RLN-FORTPEA04", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEA04
            "RLN-FORTPEA05": ForCompoundingEventIdentificationService(
                "RLN-FORTPEA05", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEA05
            "RLN-FORTPEB01": ForCompoundingEventIdentificationService(
                "RLN-FORTPEB01", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEB01
            "RLN-FORTPEB02": ForCompoundingEventIdentificationService(
                "RLN-FORTPEB02", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEB02
            "RLN-FORTPEB03": ForCompoundingEventIdentificationService(
                "RLN-FORTPEB03", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEB03
            "RLN-FORTPEB04": ForCompoundingEventIdentificationService(
                "RLN-FORTPEB04", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEB04
            "RLN-FORTPEB05": ForCompoundingEventIdentificationService(
                "RLN-FORTPEB05", reporting_site_configuration, self._mdr
            ),  # RLN-FORTPEB05
            # FRA - CONTINUOUS
            "RLN-FRACMP511": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP511", reporting_site_configuration, self._mdr
            ),  # 511
            "RLN-FRACMP512": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP512", reporting_site_configuration, self._mdr
            ),  # 512
            "RLN-FRACMP513": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP513", reporting_site_configuration, self._mdr
            ),  # 513
            "RLN-FRACMP521": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP521", reporting_site_configuration, self._mdr
            ),  # 521
            "RLN-FRACMP522": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP522", reporting_site_configuration, self._mdr
            ),  # 522
            "RLN-FRACMP523": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP523", reporting_site_configuration, self._mdr
            ),  # 523
            "RLN-FRACMP524": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP524", reporting_site_configuration, self._mdr
            ),  # 524
            "RLN-FRACMP525": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP525", reporting_site_configuration, self._mdr
            ),  # 525
            "RLN-FRACMP526": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP526", reporting_site_configuration, self._mdr
            ),  # 526
            "RLN-FRACMP527": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP527", reporting_site_configuration, self._mdr
            ),  # 527
            "RLN-FRACMP528": FraCompoundingFirstEventIdentificationService(
                "RLN-FRACMP528", reporting_site_configuration, self._mdr
            ),  # 528
            "RLN-FORPARD05": ForCompoundingEventIdentificationService(
                "RLN-FORPARD05", reporting_site_configuration, self._mdr
            ),  # RLN-FORPARD05
            "RLN-FORPARD06": ForCompoundingEventIdentificationService(
                "RLN-FORPARD06", reporting_site_configuration, self._mdr
            ),  # RLN-FORPARD06
            "RLN-FORPARD01": ForParCompoundingEventIdentificationService(
                "RLN-FORPARD01", reporting_site_configuration, self._mdr
            ),  # RLN-FORPARD01
            "RLN-FORPARD02": ForParCompoundingEventIdentificationService(
                "RLN-FORPARD02", reporting_site_configuration, self._mdr
            ),  # RLN-FORPARD01
            #new utz
            "RLN-UTZCMPL11": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL11", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL11
            "RLN-UTZCMPL12": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL12", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL12
            "RLN-UTZCMPL13": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL13", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL13
            "RLN-UTZCMPL14": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL14", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL14
            "RLN-UTZCMPL15": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL15", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL15
            "RLN-UTZCMPL16": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL16", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL16
            "RLN-UTZCMPL17": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL17", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL17
            "RLN-UTZCMPL18": UtzCompoundingEventIdentificationService(
                "RLN-UTZCMPL18", reporting_site_configuration, self._mdr
            ),  # RLN-UTZCMPL18
            "RLN-PENCMPWW1": PenCompoundingEventIdentificationService(
                "RLN-PENCMPWW1", reporting_site_configuration
            ),  # Line WW1
            "RLN-PENCMPWW2": PenCompoundingEventIdentificationService(
                "RLN-PENCMPWW2", reporting_site_configuration
            ),  # Line WW2
            "RLN-PENCMPSC1": PenCompoundingEventIdentificationService(
                "RLN-PENCMPSC1", reporting_site_configuration
            ),  # Line WSC1
            "RLN-PENCMPWW4": PenCompoundingEventIdentificationService(
                "RLN-PENCMPWW4", reporting_site_configuration
            ),  # Line WW4
            "RLN-PENCMPWW5": PenCompoundingEventIdentificationService(
                "RLN-PENCMPWW5", reporting_site_configuration
            ),  # Line WW5
            "RLN-PENCMPWW8": PenCompoundingEventIdentificationService(
                "RLN-PENCMPWW8", reporting_site_configuration
            ),  # Line WW8
            "RLN-NPTSPRN01": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRN01", reporting_site_configuration
            ),  # Line N01
            "RLN-NPTSPRWW3": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRWW3", reporting_site_configuration
            ),  # Line WW3
            "RLN-NPTSPRWW6": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRWW6", reporting_site_configuration
            ),  # Line WW6
            "RLN-NPTSPRWW7": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRWW7", reporting_site_configuration
            ),  # Line WW7
            "RLN-NPTSPRWW9": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRWW9", reporting_site_configuration
            ),  # Line WW9
            "RLN-NPTSPRW10": NptCompoundingEventIdentificationService(
                "RLN-NPTSPRW10", reporting_site_configuration
            ),  # Line WW10
            "RLN-GELVAE6K2": GELBatchEventIdentificationService(
                "RLN-GELVAE6K2", reporting_site_configuration
            ),  # GELVAE6K2
            "RLN-GELVAE6K3": GELBatchEventIdentificationService(
                "RLN-GELVAE6K3", reporting_site_configuration
            ),  # GELVAE6K3
            "RLN-GELVAE6K4": GELBatchEventIdentificationService(
                "RLN-GELVAE6K4", reporting_site_configuration
            ),  # GELVAE6K4
            "RLN-SHERDPDR1": SheRdpEventIdentificationService(
                "RLN-SHERDPDR1",
                "UNT-SHERDP",
                reporting_site_configuration,
                self._msdp,
            ),  # SHERDPDR1
            # WASHINGTON FILAMENTS
            "RLN-WASFIL2SP": WASFILEventIdentificationService(
                "RLN-WASFIL2SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL2SP
            "RLN-WASFIL3SP": WASFILEventIdentificationService(
                "RLN-WASFIL3SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL3SP
            "RLN-WASFIL4SP": WASFILEventIdentificationService(
                "RLN-WASFIL4SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL4SP
            "RLN-WASFIL5SP": WASFILEventIdentificationService(
                "RLN-WASFIL5SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL5SP
            "RLN-WASFIL6SP": WASFILEventIdentificationService(
                "RLN-WASFIL6SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL6SP
            "RLN-WASFIL7SP": WASFILEventIdentificationService(
                "RLN-WASFIL7SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL7SP
            "RLN-WASFIL8SP": WASFILEventIdentificationService(
                "RLN-WASFIL8SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL8SP
            "RLN-WASFIL10SP": WASFILEventIdentificationService(
                "RLN-WASFIL10SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL10SP
            "RLN-WASFIL11SP": WASFILEventIdentificationService(
                "RLN-WASFIL11SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL11SP
            "RLN-WASFIL12SP": WASFILEventIdentificationService(
                "RLN-WASFIL12SP",
                "UNT-WASHFIL",
                reporting_site_configuration,
                self._mdr,
            ),  # WASFIL12SP
            
        }

    def create(self, reporting_line: str) -> EventIdentification:
        return self._mapper[reporting_line]
