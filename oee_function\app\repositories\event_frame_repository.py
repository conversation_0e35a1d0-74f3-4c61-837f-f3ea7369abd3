from datetime import datetime, timezone
from typing import Optional

import pandas as pd
import pytz
from app.infra.env_variables import EnvVariables
from app.models.event_frame import Event, EventFrameLastExecutionCollection
from app.models.event_hierarchy_configuration import EventHierarchyConfiguration
from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import (
  DataModelId,
  EdgeApply,
  InstanceSort,
  Node,
  NodeApply,
  ViewId,
)
from cognite.client.data_classes.data_modeling.query import (
  NodeResultSetExpression,
  Query,
  Select,
  SourceSelector,
)
from cognite.client.data_classes.filters import And, Equals, Not, Prefix, Range
from dateutil.relativedelta import relativedelta

from ..utils.list import divide_in_chunks
from .view_repository import ViewRepository


class EventFrameRepository:
    def __init__(
        self,
        cognite_client: CogniteClient,
        view_repository: ViewRepository,
    ) -> None:
        self._cognite_client = cognite_client
        self._view_repository = view_repository
        self.variables = EnvVariables()

    def create_events(self, events: list[Event]) -> None:
        if not events:
            return
        view = self._get_view_id()
        detail_view = self._get_detail_view_id()

        nodes: list[NodeApply] = []
        edges: list[EdgeApply] = []
        for event in events:
            (node_list, edge_list) = event.convert_to_nodes_and_edges(
                event_view=view, event_detail_view=detail_view
            )

            nodes.extend(node_list)
            edges.extend(edge_list)

        paginated_nodes = divide_in_chunks(nodes, 1000)

        for entries in paginated_nodes:
            self._cognite_client.data_modeling.instances.apply(nodes=entries)

        paginated_edges = divide_in_chunks(edges, 1000)

        for entries in paginated_edges:
            self._cognite_client.data_modeling.instances.apply(edges=entries)

    def get_events_last_executions(
        self,
        events: list[EventHierarchyConfiguration],
        tz: str,
        shifts: list[pd.Timestamp],
    ):
        search_keys = {
            Event.generate_external_id_prefix(
                event.reporting_line.name, event.event_definition
            ): event
            for event in events
            if event.reporting_line.name and event.event_definition
        }

        external_ids = list(search_keys.keys())

        result = EventFrameLastExecutionCollection(search_keys)

        external_ids_paginated = [
            external_ids[50 * i : 50 * (i + 1)]
            for i in range(int(len(external_ids) / 50) + 1)
        ]
        for page_external_ids in external_ids_paginated:
            entries = self._get_events_last_executions_with_shift_validations(
                page_external_ids, tz, shifts
            )
            result.add_entries(entries)

        return result

    def delete_events(self, space: str) -> None:
        while True:
            instances = self._cognite_client.data_modeling.instances.list(
                space=space, limit=5000, sources=self._get_view_id()
            )
            if not instances:
                break
            self._cognite_client.data_modeling.instances.delete(
                nodes=instances.as_ids()
            )

    def _get_events_last_executions_with_shift_validations(
        self, page_external_ids: list[str], tz: str, shifts: list[pd.Timestamp]
    ):
        initial_months = 1
        max_months = 6

        while initial_months <= max_months:
            enternal_ids_params: dict[str, Optional[datetime]] = {
                external_id: None for external_id in page_external_ids
            }

            final_result: dict[str, datetime] = {}
            limit_date = datetime.now(timezone.utc) - relativedelta(
                months=initial_months
            )

            while enternal_ids_params:
                entries = self._get_events_last_executions(
                    enternal_ids_params, tz
                )
                enternal_ids_params = {}
                for external_id, timestamp in entries.items():
                    if (
                        self._has_shift_overlap(timestamp, shifts)
                        and timestamp.timestamp() > limit_date.timestamp()
                    ):
                        enternal_ids_params[external_id] = timestamp
                    else:
                        if timestamp.timestamp() > limit_date.timestamp():
                            final_result[external_id] = timestamp

            if final_result:
                return final_result

            initial_months += 1

        return final_result

    def _has_shift_overlap(
        self, timestamp: datetime, shifts: list[pd.Timestamp]
    ):
        for shift in shifts:
            if (
                shift.hour == timestamp.hour
                and shift.minute == timestamp.minute
                and shift.second == timestamp.second
            ):
                return True
        return False

    def _get_events_last_executions(
        self, external_ids: dict[str, Optional[datetime]], tz: str
    ):
        date_key = "startDateTime"
        view = self._get_view_id()
        query = self._build_query(view, date_key, external_ids)
        res = self._cognite_client.data_modeling.instances.query(query)

        result: dict[str, datetime] = {}
        target_timezone = pytz.timezone(tz)
        for external_id in external_ids:
            for node in res.get_nodes(external_id):
                assert type(node) is Node
                start_time = node.properties.get(view, {}).get(date_key)
                if start_time:
                    assert isinstance(start_time, str)
                    result[external_id] = (
                        datetime.fromisoformat(start_time)
                        .replace(tzinfo=timezone.utc)
                        .astimezone(tz=target_timezone)
                    )

        return result

    def _build_query(
        self,
        view: ViewId,
        date_key: str,
        external_ids: dict[str, Optional[datetime]],
    ):
        property_ref = view.as_property_ref(date_key)
        property_ref_isOpen = view.as_property_ref("isOpen")

        search_nodes = {
            external_id: NodeResultSetExpression(
                filter=(
                    And(
                        Prefix(["node", "externalId"], external_id),
                        Not(Equals(property_ref_isOpen, True)),)
                    if not last_datetime
                    else And(
                        Prefix(["node", "externalId"], external_id),
                        Not(Equals(property_ref_isOpen, True)),
                        Range(
                            property_ref,
                            lt=last_datetime.replace(
                                microsecond=0
                            ).isoformat(),
                        ),
                    )
                ),
                sort=[
                    InstanceSort(
                        property_ref,
                        direction="descending",
                    )
                ],
                limit=1,
            )
            for external_id, last_datetime in external_ids.items()
        }
        return Query(
            with_=search_nodes,  # type: ignore
            select={
                node: Select(
                    [SourceSelector(view, [date_key])],
                )
                for node in search_nodes.keys()
            },
        )

    def _get_view_id(self):
        return self._view_repository.get_view_id("OEEEvent")

    def _get_detail_view_id(self):
        return self._view_repository.get_view_id("OEEEventDetail")
    
    def query_existing_oee_events(self):
        return """
        query MyQuery($externalIds: [ID!]!, $after: String) {
            listOEEEvent(
                first: 1000
                filter: { externalId: { in: $externalIds } }
                after: $after
            ) {
                items {
                    externalId
                    status
                    isOpen
                    isManual
                    isDeleted
                }
                pageInfo {
                    endCursor
                    hasNextPage
                }
            }
        }
        """
    
    def get_existing_events(self, external_ids: list[str]) -> pd.DataFrame:
        """
        Recovery all existing events for a list of external_ids, ensuring complete search via pagination.

        Args:
            external_ids (list[str]): List of external_ids to search for.

        Returns:
            dict[str, dict]: Dictionary with external_id as key and event as value.
        """
        query = self.query_existing_oee_events()  # Get Query
        variables = {"externalIds": external_ids, "after": None}
        
        existing_events = {}

        #  Create Data Model Identifier
        data_model_id = DataModelId(
            space=self.variables.cognite.data_model_space,
            external_id=self.variables.cognite.data_model_external_id,
            version=self.variables.cognite.data_model_version
        )

        while True:
            # Pass query to Cognite Client
            response = self._cognite_client.data_modeling.graphql.query(
                id=data_model_id,
                query=query,
                variables=variables
            )

            if not response or "listOEEEvent" not in response:
                break

            # return data of query response
            data = response["listOEEEvent"]

            if not data or "items" not in data:
                break

            for event in data["items"]:
                external_id = event["externalId"]
                existing_events[external_id] = event

            # check if there is more data to be fetched
            page_info = data["pageInfo"]
            if not page_info["hasNextPage"]:
                break

            variables["after"] = page_info["endCursor"]
        
        if not existing_events: 
            df = pd.DataFrame() 
        else:
            df = pd.DataFrame.from_dict(existing_events, orient='index')

        return df
    