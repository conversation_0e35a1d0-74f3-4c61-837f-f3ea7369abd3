from typing import Any, Optional

import pandas as pd

from app.models.reporting_site_configuration import ReportingSiteConfiguration


class NanAnhydrideAceticEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus1 < 600 & ProductionLineStatus2 < 600
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus1"] < 600)
                & (data["ProductionLineStatus2"] < 600)
                & ((data["ProductionLineStatus1"].shift(1) >= 600)
                | (data["ProductionLineStatus2"].shift(1) >= 600))
            )
        )

        # event end - ProductionLineStatus1 >= 600 & ProductionLineStatus2 >= 600 (both coming from a state < 600)
        data = data.assign(
            event1a_end=(
              ((data["ProductionLineStatus1"] > 600)
               & (data["ProductionLineStatus2"] > 600))
               & ((data["ProductionLineStatus1"].shift(1) <= 600)
               | (data["ProductionLineStatus2"].shift(1) <= 600))
              )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type II event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus1 > 600 & ProductionLineStatus2 > 600
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus1"] > 600)
                & (data["ProductionLineStatus2"] > 600)
                & (data["ProductionLineStatus1"] <= 780)
                & (data["ProductionLineStatus2"] <= 780)
                & ((data["ProductionLineStatus1"].shift(1) <= 600)
                | (data["ProductionLineStatus2"].shift(1) <= 600))
            )
        )

        # event end - ProductionLineStatus1 > 780 & ProductionLineStatus2 > 780 (both coming from a state <= 780)
        data = data.assign(
            event2a_end=(
              ((data["ProductionLineStatus1"] > 780)
               & (data["ProductionLineStatus2"] > 780))
               & ((data["ProductionLineStatus1"].shift(1) <= 780)
               | (data["ProductionLineStatus2"].shift(1) <= 780))
              )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        pass

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass

    def get_inactive_value(self) -> Optional[str]:
        return None