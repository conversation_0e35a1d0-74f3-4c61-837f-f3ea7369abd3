from datetime import datetime, time
from typing import Any, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from app.infra.logger_adapter import get_logger
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.services.base_service import BaseService
from app.services.tag_value_mapping.fra.frac_pom_product_description_mapping_service import (
  FracPomProductDescriptionMappingService,
)
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value

log = get_logger()
class FracPomEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration
        self.base_service = BaseService()
        
        self._day_data = None
        self._rlt_key = "rlt"
        self._rlt_no_demand_key = "rlt_no_demand"

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - (ProductionLineStatus4 = 'Ein' (0) & ProductionLineStatus1 <= 1000 & ProductionLineStatus2 <= 1000 & ProductionLineStatus3 <= 1000)
        # OR (ProductionLineStatus4 = 'Aus' (1) & ProductionLineStatus1 <= 1000 & ProductionLineStatus2 <= 1000)
        data = data.assign(
            event1a_start=(
               (
                 (data["ProductionLineStatus4"] == 1) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000) & (data["ProductionLineStatus3"] <= 1000)
               ) |
               (
                 (data["ProductionLineStatus4"] == 0) & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
               )
            )
        )

        # event end - (ProductionLineStatus4 = 'Ein' (0) & (ProductionLineStatus1 > 1000 or ProductionLineStatus2 > 1000 or ProductionLineStatus3 > 1000) )
        # OR (ProductionLineStatus4 = 'Aus' (1) & (ProductionLineStatus1 > 1000 or ProductionLineStatus2 > 1000))
        data = data.assign(
            event1a_end=(
               (
                 (data["ProductionLineStatus4"] == 1) & (
                    ( (data["ProductionLineStatus1"] > 1000) & (data["ProductionLineStatus1"].shift(1) <= 1000) ) |
                    ( (data["ProductionLineStatus2"] > 1000) & (data["ProductionLineStatus2"].shift(1) <= 1000) ) |
                    ( (data["ProductionLineStatus3"] > 1000) & (data["ProductionLineStatus3"].shift(1) <= 1000) )
                 )
               ) |
               (
                 (data["ProductionLineStatus4"] == 0) & (
                 ( (data["ProductionLineStatus1"] > 1000) & (data["ProductionLineStatus1"].shift(1) <= 1000) ) |
                 ( (data["ProductionLineStatus2"] > 1000) & (data["ProductionLineStatus2"].shift(1) <= 1000) )
                )
              )
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(self):
        pass

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        day_data = self.__create_day_data(data)
        
        if day_data.empty:
            return day_data

        day_data = day_data[day_data[self._rlt_no_demand_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_no_demand_key]

        return day_data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        day_data = self.__create_day_data(data)

        if day_data.empty:
            return day_data

        day_data = day_data[day_data[self._rlt_key] > 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        day_data = self.__create_day_data(data)

        if day_data.empty:
            return day_data


        day_data = day_data[day_data[self._rlt_key] < 0]

        day_data["total_duration_seconds"] = day_data[self._rlt_key]

        return day_data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass
    
    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """
        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data


        total_produced_key = "total_produced"
        KGS_TO_MT_DIVISION_FACTOR = 1000

        data[total_produced_key] = np.where(
            data['ProductionLineStatus4'] == 1,
            (data["NetProduction1"] + data["NetProduction2"] + data["NetProduction3"]) * (data["ConversionPITag"] / 100) * data["CorrectionFactor"],
            (data["NetProduction1"] + data["NetProduction2"]) * (data["ConversionPITag"] / 100) * data["CorrectionFactor"]
        )

        data[total_produced_key] = data[total_produced_key] / KGS_TO_MT_DIVISION_FACTOR
        data["TotalMass"] = (data[total_produced_key] / 3600) * data["dt"]
        # data[total_produced_key] = data["TotalMass"] 
        data.set_index('index', inplace=True)
        data["NetProduction"] = data['TotalMass'].rolling('24h').sum()
        data.reset_index(inplace=True)

        per_hour_to_per_sec_factor = 1 / 3600

        data[total_produced_key] = (
            (
                data[total_produced_key]
                * per_hour_to_per_sec_factor
                * data["dt"]
            )
        ) 

        running_time_key = "running_time"
        # data[running_time_key] = 0
        
        # Modified running_time calculation
        not_running_condition = (
            (
                (data["ProductionLineStatus4"] == 1)
                & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
                & (data["ProductionLineStatus3"] <= 1000)
            )
            | (
                (data["ProductionLineStatus4"] == 0)
                & (data["ProductionLineStatus1"] <= 1000)
                & (data["ProductionLineStatus2"] <= 1000)
            )
        )
        
        data['condition_running_time'] = not_running_condition
        
        data["duration_in_seconds_without_conditions"] = (
            (data["index"].diff().dt.total_seconds().fillna(0))
        )

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"],
            dropna=False,
        ).agg(
            {
                total_produced_key: "sum",
                "duration_in_seconds": "sum",
                "ProductionLineStatus1": "mean",
                "ProductionLineStatus2": "mean",
                "ProductionLineStatus3": "mean",
                "ProductionLineStatus4": "mean",
            }
        )
        # reset multiindex, timestamp and product become columns
        data = data.reset_index().rename(columns={"index": "timestamp"})
        
        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )
        
        data.drop(columns=["duration_in_seconds"], inplace=True)


        # timestamps can be repeated for multiple products (product tag's value during the hour)
        # this gets unique timestamps based on products with the highest total_produced value for that hour
        data = data.loc[data.groupby("timestamp")["total_produced"].idxmax()]

        data_to_get_products_from = data.copy().drop(
            labels=[
                col
                for col in data.columns
                if col
                not in ["timestamp", "ProductDescription", total_produced_key]
            ],
            axis=1,
        )

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month

        mapper = FracPomProductDescriptionMappingService()
        data = mapper.map(data, "ProductDescription")
        
        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )

        msdp_data = self._msdp.loc[filter_msdp, :]

        msdp_key = "msdp"
        scheduled_rate_key = "scheduledRate"

        per_day_to_per_hour_factor = 1 / 24

        data[[msdp_key, scheduled_rate_key]] = (
            data.apply(
                self.get_msdp_value,
                msdp_data=msdp_data,
                data_aux=[msdp_key, scheduled_rate_key],
                axis=1,
                result_type="expand",
            )
        )

        rlt_key = self._rlt_key
        rlt_no_demand_key = self._rlt_no_demand_key

        tolerance = 0.001
        

        rlt_schema = [
            ( 
                "3a", 
                lambda df: (
                    (
                        (df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] <= 1000)
                        & (df["ProductionLineStatus2"] <= 1000)
                        & (df["ProductionLineStatus3"] <= 1000)
                    )
                    | (
                        (df["ProductionLineStatus4"] == 1)
                        & (df["ProductionLineStatus1"] <= 1000)
                        & (df["ProductionLineStatus2"] <= 1000)
                    )
                ),
                {rlt_key: lambda _: 0},  
            ),
            (
                "3b",
                lambda df: (df[total_produced_key] - (df[msdp_key] / 24)).abs()
                < tolerance,
                {rlt_key: lambda _: 0},
            ),
            (
                "3c1",
                lambda df: (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0) & (df["ProductionLineStatus1"] > 1000) | (df["ProductionLineStatus2"] > 1000) | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                ),
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24)
                },
            ),
            (
                "3c2",
                lambda df: ~(
                    (
                        (df[total_produced_key] <= (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                ),
                {
                    rlt_key: lambda df: (
                        (
                            (df[msdp_key] * (df[running_time_key] / 24))
                            - df[total_produced_key]
                        )
                        / (df[msdp_key] / 24)
                    )
                },
            ),
            (
                "3d1",
                lambda df: (
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key]) / 24)
                    > tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                ),
                {
                    rlt_key: lambda df: (
                        (
                            (df[scheduled_rate_key] / 24)
                            - df[total_produced_key]
                        )
                        / (df[scheduled_rate_key] / 24)
                    ),
                    rlt_no_demand_key: lambda df: (
                        ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24))
                        / (df[msdp_key] / 24)
                    ),
                },
            ),
            (
                "3d2",
                lambda df: ~((
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24))
                    > tolerance
                )
                & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                )
                & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_key: lambda df: (
                        (
                            (
                                df[scheduled_rate_key]
                                * (df[running_time_key] / 24)
                            )
                            - df[total_produced_key]
                        )
                        / (df[scheduled_rate_key] / 24)
                    ),
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * (df[running_time_key] / 24))
                        - (
                            df[scheduled_rate_key]
                            * (df[running_time_key] / 24)
                        )
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3e1",
                lambda df: ((
                    ((df[msdp_key] / 24) - df[total_produced_key]) > tolerance
                )
                & (
                    (df[total_produced_key] - (df[scheduled_rate_key] / 24))
                    > tolerance
                )
                & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3e2",
                lambda df: ~((
                    ((df[msdp_key] / 24) - df[total_produced_key]) > tolerance)
                    & (
                        (df[total_produced_key] - (df[scheduled_rate_key] / 24))
                        > tolerance)
                    & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_no_demand_key: lambda df: (
                        (df[msdp_key] * (df[running_time_key] / 24))
                        - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3f1",
                lambda df: ((
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    > tolerance
                ) & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                ) & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_key: lambda df: (
                        (df[msdp_key] / 24) - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3f2",
                lambda df: ~((
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    > tolerance
                ) & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key])
                    > tolerance
                ) & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_key: lambda df: (
                        ((df[msdp_key] / 24) * (df[running_time_key] / 24))
                        - df[total_produced_key]
                    )
                    / (df[msdp_key] / 24),
                },
            ),
            (
                "3g",
                lambda df: ((
                    ((df[msdp_key] / 24) - (df[scheduled_rate_key] / 24)).abs()
                    > tolerance
                ) & (
                    ((df[scheduled_rate_key] / 24) - df[total_produced_key]).abs()
                    > tolerance
                ) & (
                    (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & ((df["ProductionLineStatus4"] == 0)
                        & (df["ProductionLineStatus1"] > 1000)
                        | (df["ProductionLineStatus2"] > 1000)
                        | (df["ProductionLineStatus3"] > 1000))
                    )
                    | (
                        (df[total_produced_key] > (df[msdp_key] / 24))
                        & (df["ProductionLineStatus4"] == 1)
                        & (
                            (df["ProductionLineStatus1"] > 1000)
                            | (df["ProductionLineStatus2"] > 1000)
                        )
                    )
                )),
                {
                    rlt_no_demand_key: lambda df: (
                        ((df[msdp_key] / 24) * (df[scheduled_rate_key] / 24))/(df[msdp_key] / 24)
                    )
                    / (df[msdp_key] / 24),
                },
            ),
        ]

        data[rlt_key] = pd.Series(dtype="float")
        data[rlt_no_demand_key] = pd.Series(dtype="float")

        condition_key = "rlt_condition"
        data[condition_key] = pd.NA

        for (
            rlt_condition_code,
            get_filter_mask,
            target_col_to_calc_func_map,
        ) in rlt_schema:
            filter_mask = (
                get_filter_mask(data)
                & data[rlt_key].isna()
                & data[rlt_no_demand_key].isna()
            )
            for target_col, calc_func in target_col_to_calc_func_map.items():
                data.loc[filter_mask, target_col] = calc_func(
                    data.loc[filter_mask]
                )
                data.loc[filter_mask, condition_key] = rlt_condition_code

        data[rlt_key] = data[rlt_key].fillna(0)
        data[rlt_no_demand_key] = data[rlt_no_demand_key].fillna(0)
        
        # no events should be generated if msdp_key <= 0.1
        data = data[data[msdp_key] > 0.1]

        data = (
            data.groupby(
                pd.Grouper(key="timestamp", freq="D"),
            )
            .agg(
                {
                    msdp_key: "sum",
                    scheduled_rate_key: "sum",
                    total_produced_key: "sum",
                    rlt_key: "sum",
                    rlt_no_demand_key: "sum",
                    running_time_key: "sum",
                },
            )
            .reset_index()
        )

        # no events should be generated if total_produced <= 0.1
        data = data[data[total_produced_key] > 0.1]
        
        # no events should be generated if running_time <= 0.1
        data = data[data[running_time_key] > 0.1]

        data["start_time"] = data["timestamp"]
        data["end_time"] = data["timestamp"] + pd.Timedelta(days=1)

        if data.shape[0] == 0:
            self._day_data = data
            return data.copy()

        # get products reported by tag on start_time
        data = data.merge(
            data_to_get_products_from,
            how="left",
            left_on="start_time",
            right_on="timestamp",
            suffixes=(None, "_to_exclude"),
        )
        
        mapper = FracPomProductDescriptionMappingService()
        data = mapper.map(data, "ProductDescription")

        # get msdp and scheduled rate on start_time
        data["Year"] = data["start_time"].dt.year
        data["Month"] = data["start_time"].dt.month
        data["Day"] = data["start_time"].dt.day
        data[[msdp_key, scheduled_rate_key]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=[msdp_key, scheduled_rate_key],
            axis=1,
            result_type="expand",
        )

        msdp_eq_scheduled_rate_eq_total_produced = (
            ((data[total_produced_key] - data[msdp_key]).abs() < tolerance)
            & (
                (data[total_produced_key] - data[scheduled_rate_key]).abs()
                < tolerance
            )
            & ((data[msdp_key] - data[scheduled_rate_key]).abs() < tolerance)
        )

        # no events should be generated if msdp = scheduled rate = total_produced
        data = data[~msdp_eq_scheduled_rate_eq_total_produced]

        data = data.rename(columns={"timestamp": "index"}).drop(
            columns=["ProductDescription"]
        )

        hours_to_seconds_factor = 3600
        data[rlt_key] = data[rlt_key] * hours_to_seconds_factor
        data[rlt_no_demand_key] = (
            data[rlt_no_demand_key] * hours_to_seconds_factor
        )

        data.replace([np.inf, -np.inf], np.nan, inplace=True)
        data = data.dropna()

        self._day_data = data

        return data.copy()


    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: Union[str, List[str]]
    ) -> Union[float, Tuple[float, float]]:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float or tuple
        """
        product = row.get("ProductDescription", None)
        product_filter = None
        
        if product is not None:
            product_filter = msdp_data["productGroup"].str.replace("\xa0", " ") == product.replace("\xa0", " ")
            
        return msdp_util_get_msdp_value(row, msdp_data, data_aux, product_filter)

    def get_inactive_value(self) -> Optional[str]:
        return None

    def _split_intervals(
        self, row: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> list:
        """
        splits the event frames into intervals based on the business rules defined. An event should end (and another should start) when:
        :param row: row containing the start time and the end times
        :type row: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: list of the events within the original events
        :rtype: list
        """

        # get variables to start split analysis
        intervals = []
        start = row[f"event{event_definition}_start"]
        end = row[f"event{event_definition}_end"]
        current_time = start

        # iterate through each of the timespans within the
        # original time frame

        shifts_times: list[pd.Timestamp] = [
            pd.Timestamp(shift) for shift in shifts
        ]

        while current_time < end:
            # build a candidate list to be the next time
            candidates = []

            for shift_time in shifts_times:
                entry = current_time.replace(
                    hour=shift_time.hour,
                    minute=shift_time.minute,
                    second=0,
                    microsecond=000000,
                )  # type: ignore
                if shift_time.hour == 0 and shift_time.minute == 0:
                    entry = entry + pd.DateOffset(days=1)
                candidates.append(entry)
            candidates.append(end)

            # find the closest timestamp and ensure it is newer than
            # the present timestamp
            next_time = min(candidates)
            is_next_time_lower = next_time <= current_time

            while is_next_time_lower:
                candidates.remove(next_time)
                next_time = min(candidates)
                is_next_time_lower = next_time <= current_time

            # append to the list a tuple with the timestamp of start, the
            # selected end and the status code. Update the current time
            to_append = [current_time, next_time]
            for col in row.index:
                if col not in [
                    f"event{event_definition}_start",
                    f"event{event_definition}_end",
                ]:
                    to_append.append(row[col])
            intervals.append(tuple(to_append))
            current_time = next_time

        return intervals

    def _identify_working_shifts_transitions(
        self, data: pd.DataFrame, event_definition: str, shifts: list[str]
    ) -> pd.DataFrame:
        """
        identifies working shifts transitions during events

        :param data: events data with start and end times
        :type data: pd.DataFrame
        :param event_definition: definition of the event we are splitting
        :type event_definition: str
        :return: event data with working shifts transitions
        :rtype: pd.DataFrame
        """

        # split intervals according to business rules
        new_rows = data.apply(
            self._split_intervals,
            event_definition=event_definition,
            shifts=shifts,
            axis=1,
        ).explode()

        # create data frame
        events_data = pd.DataFrame(new_rows.tolist(), columns=data.columns)

        return events_data

    def create_dataframe_loss_1a_event(
          self, data: pd.DataFrame
      ) -> pd.DataFrame:

        # FRA POM demands creating events 1a to calculate running time
        df_losses_1a = self.identify_events_typeIa(data)

        df_losses_1a.dropna(
            subset=[f"event1a_start", f"event1a_end"],
            inplace=True,
        )

        df_losses_1a.set_index("index", inplace=True)

        df_losses_1a = self.base_service.create_event_dataframe(df_losses_1a, '1a')

        shift_frapom = ['2024-01-01 05:30:00', '2024-01-01 17:30:00', '2024-01-01 00:00:00']
        df_losses_1a = self._identify_working_shifts_transitions(df_losses_1a, '1a', shift_frapom)

        df_losses_1a = self.base_service.calculate_time_duration(df_losses_1a, 'event1a_start', 'event1a_end')

        df_losses_1a['event1a_date_loss'] = df_losses_1a['event1a_start'].dt.normalize()

        df_losses_1a.drop(columns = ['event1a_start', 'event1a_end', 'event_definition'], inplace = True)

        df_losses_1a.rename(columns = {'total_duration_seconds': 'loss_duration_seconds'}, inplace = True)

        df_losses_1a = df_losses_1a.groupby('event1a_date_loss', as_index=False).agg({'loss_duration_seconds': 'sum'})

        return df_losses_1a