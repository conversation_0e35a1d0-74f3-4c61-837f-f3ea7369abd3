from datetime import datetime

from cognite.client import CogniteClient


class TimeSeriesRepository:
    def __init__(self, cognite_client: CogniteClient) -> None:
        self._cognite_client = cognite_client

    def retrieve_dataframe(
        self, external_ids: set[str], start_time: datetime, end_time: datetime
    ):
        ts_results = self._cognite_client.time_series.data.retrieve_dataframe(
            external_id=list(external_ids),
            start=start_time,
            end=end_time,
        )
                
        return ts_results

    def retrieve_configurations(
        self, external_ids: set[str]
    ):
        ts_results = self._cognite_client.time_series.retrieve_multiple(
            external_ids=list(external_ids),
        ).to_pandas()
        return ts_results

        
