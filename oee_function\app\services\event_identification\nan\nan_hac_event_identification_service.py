from typing import Any, Optional
from datetime import time

import pandas as pd
from app.models.reporting_site_configuration import ReportingSiteConfiguration
from app.utils.msdp_utils import get_msdp_value as msdp_util_get_msdp_value


class NanHacEventIdentificationService:
    def __init__(
        self,
        reporting_line_external_id: str,
        reporting_unit_external_id: str,
        reporting_site_configuration: ReportingSiteConfiguration,
        msdp: pd.DataFrame,
    ) -> None:
        self._msdp = msdp
        self._reporting_line_external_id = reporting_line_external_id
        self._reporting_unit_external_id = reporting_unit_external_id
        self._reporting_site_configuration = reporting_site_configuration

        self._day_data = None

        # create maps to identify events
        self.events_identification_methods = {
            "1a": self.identify_events_typeIa,
            "1b": self.identify_events_typeIb,
            "1c": self.identify_events_typeIc,
            "1d": self.identify_events_typeId,
            "2a": self.identify_events_typeIIa,
            "2b": self.identify_events_typeIIb,
            "2c": self.identify_events_typeIIc,
            "3a": self.identify_events_typeIIIa,
            "3b": self.identify_events_typeIIIb,
            "3c": self.identify_events_typeIIIc,
            "3d": self.identify_events_typeIIId,
            "4a": self.identify_events_typeIVa,
            "4b": self.identify_events_typeIVb,
        }

    def get_events_identification_methods(self) -> dict[str, Any]:
        return self.events_identification_methods

    def identify_events_typeIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type Ia

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus_1 = Closed & ProductionLineStatus_2 = Closed & TotalFeed < 0
        data = data.assign(
            event1a_start=(
                (data["ProductionLineStatus_1"] == 0)
                & (data["ProductionLineStatus_2"] == 0)
                & (data["TotalFeed"] < 1)
            )
        )

        # event end - ProductionLineStatus_1 = Open & ProductionLineStatus_2 = Open & TotalFeed > 0
        data = data.assign(
            event1a_end=(
                (data["ProductionLineStatus_1"] == 1)
                & (data["ProductionLineStatus_2"] == 1)
                & (data["TotalFeed"] > 1)
            )
        )

        # correct start and end flags
        data = data.assign(
            event1a_start=(
                (data["event1a_start"] == True)
                & (data["event1a_start"].shift(1) != True)
            )
        )

        data = data.assign(
            event1a_end=(
                (data["event1a_end"] == True)
                & (data["event1a_end"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIb(self):
        pass

    def identify_events_typeIc(self):
        pass

    def identify_events_typeId(self):
        pass

    def identify_events_typeIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies the events of type IIa

        :param data: time series data with the status of the line
        :type data: pd.DataFrame
        :return: data with tags which indicate the start and the end time of each type I event
        :rtype: pd.DataFrame
        """

        # event trigger start - ProductionLineStatus_1 = Open & ProductionLineStatus_2 = Open & TotalFeed > 0
        data = data.assign(
            event2a_start=(
                (data["ProductionLineStatus_1"] == 1)
                & (data["ProductionLineStatus_2"] == 1)
                & (data["TotalFeed"] > 1)
            )
        )

        # event end - ProductionLineStatus_3 < 950
        data = data.assign(
            event2a_end=(
                (data["ProductionLineStatus_3"] < 950)
                & (data["ProductionLineStatus_3"].shift(1) >= 950)
            )
        )

        # correct start and end flags
        data = data.assign(
            event2a_start=(
                (data["event2a_start"] == True)
                & (data["event2a_start"].shift(1) != True)
            )
        )

        return data

    def identify_events_typeIIb(self):
        pass

    def identify_events_typeIIc(self):
        pass

    def identify_events_typeIIIa(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIa

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3a_end=(
                (data["MSDP"] > data["SCHR"])
                & (data["SCHR"] > data["NetProduction"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
                & (data["running_time"] > 0.15)
            )
        )
        data = data.assign(
            event3a_start=(data["event3a_end"])
        )
        data = data.assign(
            total_duration_seconds=(
                ((data["SCHR"] * data["running_time"]/24) - data["NetProduction"])
                / (data["SCHR"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIb(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIb

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3b_end=(
                (data["MSDP"] > data["SCHR"])
                & (data["SCHR"] > data["NetProduction"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
                & (data["running_time"] > 0.15)
            )
        )

        data = data.assign(
            event3b_start=(data["event3b_end"])
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["SCHR"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIIc(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIIc

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3c_end=(
                (data["MSDP"] > data["NetProduction"])
                & (data["NetProduction"] > data["SCHR"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
                & (data["running_time"] > 0.15)
            )
        )

        data = data.assign(
            event3c_start=(data["event3c_end"])
        )

        data = data.assign(
            total_duration_seconds=(
                (data["MSDP"] - data["NetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIIId(
        self, data: pd.DataFrame, **args
    ) -> pd.DataFrame:
        """
        identifies events of type IIId

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        data = self.__create_day_data(data)

        data = data.assign(
            event3d_end=(
                (data["NetProduction"] > data["MSDP"])
                & (data["NetProduction"] > 0)
                & (data["NetProduction"] != data["MSDP"])
                & (data["is_midnight"])
                & (data["running_time"] > 0.15)
            )
        )

        data = data.assign(
            event3d_start=(data["event3d_end"])
        )

        data = data.assign(
            total_duration_seconds=(
                ((data["MSDP"] * data["running_time"]/24) - data["NetProduction"]) / (data["MSDP"] / 24)
            )
            * 3600
        ).dropna(subset=["total_duration_seconds"])

        return data

    def identify_events_typeIVa(self, data: pd.DataFrame, **args):
        pass

    def identify_events_typeIVb(self):
        pass

    def __create_day_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        create day data to support events 3a, 3b and 3c

        :param data: dataframe with the events identified
        :type data: pd.DataFrame
        :param prod_rate_data: dataframe with the production flow rates
        :type prod_rate_data: pd.DataFrame
        :return: dataframe with the events of type III included
        :rtype: pd.DataFrame
        """

        if self._day_data is not None:
            return self._day_data.copy()

        first_timestamp = data["index"].head(1).iloc[0]
        last_timestamp = data["index"].tail(1).iloc[0]
        mid_night_time = time(0, 0, 0, 0)
        start_cutoff = (
            None
            if first_timestamp.time() == mid_night_time
            else first_timestamp.date()
        )
        end_cutoff = last_timestamp.date()

        # remove partial days from start and end
        data = data[
            (
                (start_cutoff is not None)
                & (data["index"].dt.date > start_cutoff)
            )
            & ((end_cutoff is not None) & (data["index"].dt.date < end_cutoff))
        ]

        if data.empty:
            return data

        data.set_index("index", inplace = True)

        dtypes = data.dtypes

        for column in data.columns:
            data[column] = data[column].astype(dtypes[column])

        data.reset_index(inplace=True)

        # drop duplicates to remove work shift index duplicated
        data = data.drop_duplicates("index", keep="first")

        data["TotalMass"] = (data["TotalFeed"] / 3600) * data["dt"]
        data.set_index("index", inplace=True)
        data["AcidProduced"] = (data["TotalMass"]/0.53)
        data.reset_index(inplace=True)

        not_running_condition = (
            (data["ProductionLineStatus_1"] == 0)
            & (data["ProductionLineStatus_2"] == 0)
            & (data["TotalFeed"] < 1)
        )

        running_time_key = "running_time"

        data["condition_running_time"] = not_running_condition

        data["duration_in_seconds"] = (
            (data["index"].diff().dt.total_seconds().fillna(0)) * (~not_running_condition)
        )

        data = data.groupby(
            [pd.Grouper(key="index", freq="1h"), "ProductDescription"], dropna=False
        ).agg(
            {
                "AcidProduced": "sum",
                "duration_in_seconds": "sum",
            }
        )

        data = data.reset_index().rename(columns={"index": "timestamp"})

        data[running_time_key] = (
            data.groupby(pd.Grouper(key="timestamp", freq="1h"))["duration_in_seconds"].transform("sum") / 3600
        )

        data.drop(columns=["duration_in_seconds"],inplace=True)

        # find the year that is in accordance with the indexed date
        data["Year"] = data["timestamp"].dt.year

        # find the month that is in accordance with the indexed date
        data["Month"] = data["timestamp"].dt.month
        data["Day"] = data["timestamp"].dt.day
        filter_msdp = (
            self._msdp["reportingLineExternalId"]
            == self._reporting_line_external_id
        )
        msdp_data = self._msdp.loc[filter_msdp, :]

        data[["MSDP", "SCHR"]] = data.apply(
            self.get_msdp_value,
            msdp_data=msdp_data,
            data_aux=["msdp", "scheduledRate"],
            axis=1,
            result_type="expand"
        )

        data["MSDP"] /= 24
        data["SCHR"] /= 24

        data = (
            data.groupby(
                [pd.Grouper(key="timestamp", freq="D"), data["ProductDescription"]],
            )
            .agg(
                {
                    "MSDP": "sum",
                    "SCHR": "sum",
                    "AcidProduced": "sum",
                    "running_time": "sum",
                },
            )
            .reset_index()
        )

        data["is_midnight"] = (
            (data["timestamp"].dt.hour == 00)
            & (data["timestamp"].dt.minute == 0)
            & (data["timestamp"].dt.second == 0)
            & (data["timestamp"].dt.microsecond == 0)
        )

        if data["is_midnight"].any():
          data = data[data["is_midnight"]]

        data = data.rename(columns={"timestamp": "index", "AcidProduced": "NetProduction"}).drop(
            columns=["ProductDescription"]
        )

        self._day_data = data

        return data

    def get_msdp_value(
        self, row: pd.Series, msdp_data: pd.DataFrame, data_aux: str
    ) -> float:
        """
        retrieves the msdp from the data stored in the contract

        :param row: row containing the product ID and start_time
        :type row: pd.Series
        :return: BBCT value
        :rtype: float
        """
        return msdp_util_get_msdp_value(row, msdp_data, data_aux)

    def get_inactive_value(self) -> Optional[str]:
        return None